import SimpleITK as sitk
import os
import numpy as np

def parse_aal_txt(txt_path):
    label2idx = {}
    with open(txt_path, encoding='utf-8') as f:
        for line in f:
            if line.strip() == '' or line.startswith('#'):
                continue
            parts = line.strip().split('\t')
            if len(parts) < 3:
                continue
            label = parts[1]
            try:
                idx = int(parts[2])
            except ValueError:
                continue
            label2idx[label] = idx
    return label2idx

# 你可以根据需要修改非皮层区标签名
NONCORTICAL_LABEL_NAMES = [
    'Amygdala_l', 'Amygdala_r', 'CaudateNucl_l', 'CaudateNucl_r',
    'Putamen_l', 'Putamen_r', 'Pallidum_l', 'Pallidum_r',
    'Thalamus_l', 'Thalamus_r', 'Medulla', 'Midbrain',
]

def intersect_aal_with_probmaps(
    aal_in_pet_path,
    gm_in_pet_path,
    csf_in_pet_path,
    wm_in_pet_path,
    output_intersected_path,
    wm_threshold=0.5,
    gm_threshold=0.5,
    csf_threshold=0.5,
    gm_mask_noncortical=True,
    use_csf_prob=True,
    aal_txt_path=None
):
    """
    AAL模板与GM/CSF概率图相交，补全白质区域。
    
    Args:
        aal_in_pet_path: AAL模板在PET空间的路径
        gm_in_pet_path: GM概率图在PET空间的路径
        csf_in_pet_path: CSF概率图在PET空间的路径
        wm_in_pet_path: WM概率图在PET空间的路径
        output_intersected_path: 输出路径
        wm_threshold: 白质概率阈值
        gm_threshold: 灰质概率阈值
        csf_threshold: 脑脊液概率阈值
        gm_mask_noncortical: 是否对非皮层区域应用GM掩码
        use_csf_prob: 是否使用CSF概率图谱
        aal_txt_path: AAL标签文件路径
    """
    if aal_txt_path is None:
        raise ValueError("必须提供AAL txt路径")
    label2idx = parse_aal_txt(aal_txt_path)
    NONCORTICAL_LABELS = {k: label2idx[k] for k in NONCORTICAL_LABEL_NAMES if k in label2idx}
    NONCORTICAL_LABEL_VALUES = set(NONCORTICAL_LABELS.values())
    output_dir = os.path.dirname(output_intersected_path)
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    aal_img = sitk.ReadImage(aal_in_pet_path)
    gm_prob_img = sitk.ReadImage(gm_in_pet_path, sitk.sitkFloat32)
    wm_prob_img = sitk.ReadImage(wm_in_pet_path, sitk.sitkFloat32)
    
    # 根据use_csf_prob参数决定是否读取CSF概率图
    if use_csf_prob:
        csf_prob_img = sitk.ReadImage(csf_in_pet_path, sitk.sitkFloat32)
        # 验证空间信息匹配
        if not all(aal_img.GetSize() == img.GetSize() and aal_img.GetSpacing() == img.GetSpacing() and aal_img.GetOrigin() == img.GetOrigin() for img in [gm_prob_img, csf_prob_img, wm_prob_img]):
            raise ValueError("输入的AAL、GM、CSF和WM图谱的空间信息不匹配！")
    else:
        # 只验证GM和WM的空间信息匹配
        if not all(aal_img.GetSize() == img.GetSize() and aal_img.GetSpacing() == img.GetSpacing() and aal_img.GetOrigin() == img.GetOrigin() for img in [gm_prob_img, wm_prob_img]):
            raise ValueError("输入的AAL、GM和WM图谱的空间信息不匹配！")
    
    gm_mask = sitk.BinaryThreshold(gm_prob_img, lowerThreshold=gm_threshold, upperThreshold=1e9, insideValue=1, outsideValue=0)
    
    # 根据use_csf_prob参数决定是否使用CSF掩码
    if use_csf_prob:
        high_csf_mask = sitk.BinaryThreshold(csf_prob_img, lowerThreshold=csf_threshold, upperThreshold=1e9, insideValue=1, outsideValue=0)
        not_csf_mask = sitk.Not(high_csf_mask)
        final_mask = sitk.Multiply(gm_mask, not_csf_mask)
    else:
        # 不使用CSF概率图，直接使用GM掩码
        final_mask = gm_mask
    
    final_mask = sitk.Cast(final_mask, aal_img.GetPixelID())
    if gm_mask_noncortical:
        intersected_aal = sitk.Multiply(aal_img, final_mask)
    else:
        aal_np = sitk.GetArrayFromImage(aal_img)
        mask_np = sitk.GetArrayFromImage(final_mask)
        out_np = np.zeros_like(aal_np)
        cortex_mask = ~np.isin(aal_np, list(NONCORTICAL_LABEL_VALUES))
        out_np[cortex_mask] = aal_np[cortex_mask] * mask_np[cortex_mask]
        noncortex_mask = np.isin(aal_np, list(NONCORTICAL_LABEL_VALUES))
        out_np[noncortex_mask] = aal_np[noncortex_mask]
        intersected_aal = sitk.GetImageFromArray(out_np)
        intersected_aal.CopyInformation(aal_img)
    sitk.WriteImage(intersected_aal, output_intersected_path)
    # 补全白质区域
    wm_mask = sitk.BinaryThreshold(wm_prob_img, lowerThreshold=wm_threshold, upperThreshold=1e9, insideValue=1, outsideValue=0)
    intersected_zero_mask = sitk.BinaryThreshold(intersected_aal, lowerThreshold=0, upperThreshold=0, insideValue=1, outsideValue=0)
    wm_fill_mask = sitk.And(wm_mask, intersected_zero_mask)
    # 获取AAL标签最大值+1作为白质填充值
    aal_np = sitk.GetArrayFromImage(aal_img)
    wm_label_value = int(aal_np.max()) + 1
    wm_fill_img = sitk.Cast(wm_fill_mask, sitk.sitkUInt8) * wm_label_value
    wm_fill_img = sitk.Cast(wm_fill_img, intersected_aal.GetPixelID())
    final_with_wm = sitk.Add(intersected_aal, wm_fill_img)
    final_output_path = output_intersected_path.replace('.nii', '_withWM.nii')
    sitk.WriteImage(final_with_wm, final_output_path)
    return output_intersected_path, final_output_path 