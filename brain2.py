import subprocess
import os
import sys

def run_synthstrip_final(pet_image_path, model_path, output_brain_path, output_mask_path):
    """
    最终的、经过验证可行的Python函数，用于调用nipreps-synthstrip。
    它使用可执行文件的完整路径，并明确指定了模型的路径。
    """
    print("--- (最终自动化版) 使用完整路径和指定的模型文件进行脑提取 ---")
    
    # --- 验证所有输入路径是否存在 ---
    if not os.path.exists(pet_image_path):
        print(f"错误: 输入的PET文件不存在: {pet_image_path}")
        return False
    if not os.path.exists(model_path):
        print(f"错误: 模型文件不存在: {model_path}")
        print("请确保您已手动下载 synthstrip.pt 并提供了正确的路径。")
        return False

    # --- 自动定位 nipreps-synthstrip.exe ---
    python_exe_path = sys.executable
    scripts_dir = os.path.join(os.path.dirname(python_exe_path), 'Scripts')
    nipreps_exe_path = os.path.join(scripts_dir, 'nipreps-synthstrip.exe')
    
    if not os.path.exists(nipreps_exe_path):
        print(f"错误: 在预期的路径中找不到 'nipreps-synthstrip.exe': {nipreps_exe_path}")
        return False

    print(f"找到可执行文件: {nipreps_exe_path}")
    print(f"使用模型文件: {model_path}")

    # --- 构建最终的、完整的命令 ---
    command = [
        nipreps_exe_path,
        "--model", model_path,
        "-i", pet_image_path,
        "-o", output_brain_path,
        "-m", output_mask_path
    ]
        
    print(f"正在执行命令: {' '.join(command)}")
    
    try:
        # 使用subprocess运行命令
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        print("\n--- 命令成功完成！ ---")
        print(f"提取后的大脑PET图像已保存至: {output_brain_path}")
        print(f"大脑掩模已保存至: {output_mask_path}")
        # 打印来自工具的输出
        print("\n--- 来自工具的确认信息 ---")
        print(result.stdout.strip())
        return True
    except subprocess.CalledProcessError as e:
        print(f"错误: 'nipreps-synthstrip.exe' 执行失败 (返回码: {e.returncode})。")
        print("--- 来自工具的错误输出 (stderr) ---")
        print(e.stderr.strip())
        return False

# --- 主程序 ---
if __name__ == "__main__":
    # !! 重要 !! 请修改为您自己的路径
    # 1. 您的PET数据文件
    PET_IMAGE_FILE = "F:/Dcf/CODE/test/li_yunqiao_PET/NIFTI_Output_Folder/Series.nii.gz"
    
    # 2. 您手动下载并存放的模型文件
    MODEL_FILE = "F:/Dcf/CODE/test/models/synthstrip.1.pt" # 根据您的文件名，这里是.1.pt
    
    # 3. 输出文件夹
    OUTPUT_FOLDER = "F:/Dcf/CODE/test/output/test_PET"
    
    if not os.path.exists(OUTPUT_FOLDER):
        os.makedirs(OUTPUT_FOLDER)
        
    pet_brain_output = os.path.join(OUTPUT_FOLDER, "pet_brain_from_script.nii.gz")
    pet_mask_output = os.path.join(OUTPUT_FOLDER, "pet_brain_mask_from_script.nii.gz")
    
    # 调用这个最终的函数
    run_synthstrip_final(PET_IMAGE_FILE, MODEL_FILE, pet_brain_output, pet_mask_output)