import os
import sys
import argparse
import json
from registration import affine_registration
from transform_label_template import transform_label_template_to_pet
from transform_template import transform_template_to_pet
from intersect_aal_merged import intersect_aal_with_probmaps
from dicom_converter import convert_if_dicom, is_dicom_folder, get_dicom_info
from nii_to_dicom import nifti_to_dicom_sitk
import SimpleITK as sitk

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='PET图像处理管道')
    
    # 路径参数
    parser.add_argument('--moving-image', required=True, help='移动图像路径')  
    parser.add_argument('--fixed-image', required=True, help='固定图像路径')   
    parser.add_argument('--output-folder', required=True, help='输出文件夹路径')   
    parser.add_argument('--template', required=True, help='模板路径')   ##内置
    parser.add_argument('--gm-template', required=True, help='GM概率图路径')   ##内置
    parser.add_argument('--csf-template', required=True, help='CSF概率图路径')   ##内置
    parser.add_argument('--wm-template', required=True, help='WM概率图路径')   ##内置
    parser.add_argument('--aal-txt', required=True, help='AAL标签文件路径')   ##内置
    
    # DICOM相关参数
    parser.add_argument('--moving-series-desc', help='移动图像DICOM序列描述过滤条件')
    parser.add_argument('--fixed-series-desc', help='固定图像DICOM序列描述过滤条件')
    parser.add_argument('--auto-convert-dicom', action='store_true', default=True, 
                       help='自动转换DICOM序列（默认启用）')
    parser.add_argument('--dicom-output-subfolder', default='converted', 
                       help='DICOM转换输出子文件夹名称（默认: converted）')
    
    # 输出DICOM参数
    parser.add_argument('--output-dicom', action='store_true', default=True,
                       help='将最终结果转换为DICOM格式（默认启用）')
    parser.add_argument('--dicom-series-desc', default='Processed AAL Template',
                       help='输出DICOM的序列描述（默认: Processed AAL Template）')
    parser.add_argument('--dicom-output-folder', help='DICOM输出文件夹，默认在output-folder下创建dicom子文件夹')
    
    # 概率阈值参数
    parser.add_argument('--wm-threshold', type=float, default=0.5, help='白质概率阈值 (默认: 0.5)')  ##需要前端输入
    parser.add_argument('--gm-threshold', type=float, default=0.5, help='灰质概率阈值 (默认: 0.5)')  ##需要前端输入
    parser.add_argument('--csf-threshold', type=float, default=0.5, help='脑脊液概率阈值 (默认: 0.5)')  ##需要前端输入
    
    # 处理参数
    parser.add_argument('--gm-mask-noncortical', type=bool, default=False, help='是否对非皮层区域应用GM掩码 (默认: False)')  ##需要前端输入
    parser.add_argument('--use-csf-prob', type=bool, default=False, help='是否使用CSF概率图谱 (默认: False)')  ##需要前端输入
    
    # 配准参数
    parser.add_argument('--histogram-bins', type=int, default=50, help='直方图箱数 (默认: 50)')
    parser.add_argument('--learning-rate', type=float, default=2.0, help='学习率 (默认: 2.0)')
    parser.add_argument('--min-step', type=float, default=1e-6, help='最小步长 (默认: 1e-6)')
    parser.add_argument('--iterations', type=int, default=300, help='迭代次数 (默认: 300)')
    parser.add_argument('--relaxation-factor', type=float, default=0.8, help='松弛因子 (默认: 0.8)')
    
    # 输出格式
    parser.add_argument('--output-format', choices=['json', 'text'], default='json', help='输出格式 (默认: json)')
    
    return parser.parse_args()

def process_input_path(input_path, series_desc=None, output_folder=None, auto_convert=True, subfolder='converted'):
    """
    处理输入路径，如果是DICOM则转换为NIFTI
    
    Args:
        input_path: 输入路径
        series_desc: 序列描述过滤条件
        output_folder: 输出文件夹
        auto_convert: 是否自动转换DICOM
        subfolder: DICOM转换输出子文件夹
    
    Returns:
        str: 处理后的文件路径
    """
    if not auto_convert:
        return input_path
    
    try:
        if is_dicom_folder(input_path):
            print(f"检测到DICOM文件夹: {input_path}")
            
            # 获取DICOM信息
            dicom_info = get_dicom_info(input_path)
            if dicom_info:
                print(f"DICOM信息: {dicom_info['series_count']} 个系列, {dicom_info['file_count']} 个文件")
                print(f"图像尺寸: {dicom_info['size']}")
                if dicom_info['series_description'] != "Unknown":
                    print(f"序列描述: {dicom_info['series_description']}")
            
            # 设置转换输出路径
            if output_folder:
                dicom_output_folder = os.path.join(output_folder, subfolder)
            else:
                dicom_output_folder = os.path.join(os.path.dirname(input_path), subfolder)
            
            # 转换DICOM到NIFTI
            converted_path = convert_if_dicom(input_path, dicom_output_folder, series_desc)
            print(f"DICOM已转换为: {converted_path}")
            return converted_path
        else:
            return input_path
    except Exception as e:
        print(f"处理输入路径时出错 {input_path}: {e}")
        return input_path

def convert_output_to_dicom(nifti_path, output_folder, series_description):
    """
    将NIFTI输出转换为DICOM格式
    
    Args:
        nifti_path: NIFTI文件路径
        output_folder: 输出文件夹
        series_description: DICOM序列描述
    
    Returns:
        str: DICOM输出文件夹路径
    """
    try:
        print(f"将输出转换为DICOM格式: {nifti_path}")
        
        # 生成DICOM输出路径
        base_name = os.path.splitext(os.path.basename(nifti_path))[0]
        if base_name.endswith('.nii'):
            base_name = os.path.splitext(base_name)[0]
        
        dicom_output_dir = os.path.join(output_folder, f"{base_name}_dicom")
        
        # 转换为DICOM
        num_slices = nifti_to_dicom_sitk(nifti_path, dicom_output_dir, series_description)
        
        print(f"DICOM转换完成: {dicom_output_dir} (共 {num_slices} 个切片)")
        return dicom_output_dir
        
    except Exception as e:
        print(f"DICOM转换失败: {e}")
        return None

def main():
    """主函数"""
    args = parse_arguments()
    
    # 创建输出目录
    os.makedirs(args.output_folder, exist_ok=True)
    
    # 处理输入路径（自动转换DICOM）
    print("处理输入图像...")
    moving_image_processed = process_input_path(
        args.moving_image, 
        args.moving_series_desc, 
        args.output_folder, 
        args.auto_convert_dicom, 
        args.dicom_output_subfolder
    )
    
    fixed_image_processed = process_input_path(
        args.fixed_image, 
        args.fixed_series_desc, 
        args.output_folder, 
        args.auto_convert_dicom, 
        args.dicom_output_subfolder
    )
    
    # 验证处理后的文件是否存在
    input_files = [
        moving_image_processed, fixed_image_processed, args.template,
        args.gm_template, args.csf_template, args.wm_template, args.aal_txt
    ]
    
    missing_files = [f for f in input_files if not os.path.exists(f)]
    if missing_files:
        error_msg = f"错误：以下文件不存在: {', '.join(missing_files)}"
        if args.output_format == 'json':
            print(json.dumps({'error': error_msg}, ensure_ascii=False))
        else:
            print(error_msg)
        sys.exit(1)
    
    try:
        # 1. 仿射配准
        print("开始仿射配准...")
        affine_path, affined_img_path = affine_registration(
            moving_image_processed, 
            fixed_image_processed, 
            args.output_folder,
            histogram_bins=args.histogram_bins,
            learning_rate=args.learning_rate,
            min_step=args.min_step,
            iterations=args.iterations,
            relaxation_factor=args.relaxation_factor
        )
        
        # 2. 逆变换AAL标签模板
        print("变换AAL标签模板...")
        aal_in_pet_path = os.path.join(args.output_folder, "template_to_pet_inverse_affineonly.nii.gz")
        transform_label_template_to_pet(args.template, moving_image_processed, affine_path, aal_in_pet_path)
        
        # 3. 逆变换GM/CSF/WM概率图
        print("变换概率图模板...")
        gm_in_pet_path = os.path.join(args.output_folder, "gm_template_to_pet_inverse_affineonly.nii.gz")
        csf_in_pet_path = os.path.join(args.output_folder, "csf_template_to_pet_inverse_affineonly.nii.gz")
        wm_in_pet_path = os.path.join(args.output_folder, "wm_template_to_pet_inverse_affineonly.nii.gz")
        
        transform_template_to_pet(args.gm_template, moving_image_processed, affine_path, gm_in_pet_path, interp_mode="linear", output_type=None)
        
        # 根据use_csf_prob参数决定是否变换CSF模板
        if args.use_csf_prob:
            transform_template_to_pet(args.csf_template, moving_image_processed, affine_path, csf_in_pet_path, interp_mode="linear", output_type=None)
        else:
            # 如果不使用CSF概率图，创建一个全零的CSF图像作为占位符
            print("跳过CSF模板变换（use_csf_prob=False）")
            # 读取GM图像作为参考来创建全零CSF图像
            gm_img = sitk.ReadImage(gm_in_pet_path)
            zero_csf_img = sitk.Image(gm_img.GetSize(), gm_img.GetPixelID())
            zero_csf_img.SetSpacing(gm_img.GetSpacing())
            zero_csf_img.SetOrigin(gm_img.GetOrigin())
            zero_csf_img.SetDirection(gm_img.GetDirection())
            sitk.WriteImage(zero_csf_img, csf_in_pet_path)
        
        transform_template_to_pet(args.wm_template, moving_image_processed, affine_path, wm_in_pet_path, interp_mode="linear", output_type=None)
        
        # 4. AAL与概率图相交并补全白质
        print("执行AAL与概率图相交...")
        output_intersected_path = os.path.join(args.output_folder, "Centiloid.nii.gz")
        intersected_path, final_with_wm_path = intersect_aal_with_probmaps(
            aal_in_pet_path,
            gm_in_pet_path,
            csf_in_pet_path,
            wm_in_pet_path,
            output_intersected_path,
            wm_threshold=args.wm_threshold,
            gm_threshold=args.gm_threshold,
            csf_threshold=args.csf_threshold,
            gm_mask_noncortical=args.gm_mask_noncortical,
            use_csf_prob=args.use_csf_prob,
            aal_txt_path=args.aal_txt
        )
        
        # 5. 转换为DICOM格式（如果需要）
        dicom_outputs = {}
        if args.output_dicom:
            print("转换为DICOM格式...")
            
            # 设置DICOM输出文件夹
            if args.dicom_output_folder:
                dicom_base_folder = args.dicom_output_folder
            else:
                dicom_base_folder = os.path.join(args.output_folder, "dicom")
            
            os.makedirs(dicom_base_folder, exist_ok=True)
            
            # 转换主要结果文件
            dicom_outputs['intersected_aal_dicom'] = convert_output_to_dicom(
                intersected_path, 
                dicom_base_folder, 
                f"{args.dicom_series_desc} - Intersected"
            )
            
            dicom_outputs['final_with_wm_dicom'] = convert_output_to_dicom(
                final_with_wm_path, 
                dicom_base_folder, 
                f"{args.dicom_series_desc} - With WM"
            )
        
        # 输出结果
        result = {
            'status': 'success',
            'message': '流程全部完成！',
            'input_files': {
                'original_moving_image': args.moving_image,
                'original_fixed_image': args.fixed_image,
                'processed_moving_image': moving_image_processed,
                'processed_fixed_image': fixed_image_processed
            },
            'output_files': {
                'affine_transform': affine_path,
                'affined_image': affined_img_path,
                'aal_in_pet': aal_in_pet_path,
                'gm_in_pet': gm_in_pet_path,
                'csf_in_pet': csf_in_pet_path,
                'wm_in_pet': wm_in_pet_path,
                'intersected_aal': intersected_path,
                'final_with_wm': final_with_wm_path
            },
            'parameters': {
                'wm_threshold': args.wm_threshold,
                'gm_threshold': args.gm_threshold,
                'csf_threshold': args.csf_threshold,
                'gm_mask_noncortical': args.gm_mask_noncortical,
                'use_csf_prob': args.use_csf_prob,
                'auto_convert_dicom': args.auto_convert_dicom,
                'moving_series_desc': args.moving_series_desc,
                'fixed_series_desc': args.fixed_series_desc,
                'output_dicom': args.output_dicom,
                'dicom_series_desc': args.dicom_series_desc
            }
        }
        
        # 添加DICOM输出信息
        if dicom_outputs:
            result['dicom_outputs'] = dicom_outputs
        
        if args.output_format == 'json':
            print(json.dumps(result, ensure_ascii=False, indent=2))
        else:
            print("流程全部完成！")
            print(f"输入文件:")
            for key, path in result['input_files'].items():
                print(f"  {key}: {path}")
            print(f"输出文件:")
            for key, path in result['output_files'].items():
                print(f"  {key}: {path}")
            if dicom_outputs:
                print(f"DICOM输出:")
                for key, path in dicom_outputs.items():
                    print(f"  {key}: {path}")
            print(f"使用参数:")
            for key, value in result['parameters'].items():
                print(f"  {key}: {value}")
                
    except Exception as e:
        error_msg = f"处理过程中发生错误: {str(e)}"
        if args.output_format == 'json':
            print(json.dumps({'error': error_msg}, ensure_ascii=False))
        else:
            print(error_msg)
        sys.exit(1)

if __name__ == "__main__":
    main() 