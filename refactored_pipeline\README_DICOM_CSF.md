# PET图像处理管道 - DICOM支持和CSF选项

## 新增功能

### 1. DICOM序列支持
- **自动检测**：自动检测输入路径是DICOM文件夹还是NIFTI文件
- **自动转换**：如果是DICOM序列，自动转换为NIFTI格式进行处理
- **序列选择**：支持通过序列描述选择特定的DICOM系列
- **DICOM输出**：最终结果可以转换为DICOM格式输出

### 2. CSF概率图谱选项
- **可选使用**：可以选择是否使用CSF概率图谱进行掩码处理
- **灵活配置**：当不使用CSF时，只使用GM和WM概率图谱

## 使用方法

### 命令行调用

```bash
python main.py \
  --moving-image "path/to/dicom/folder" \
  --fixed-image "path/to/nifti/file.nii.gz" \
  --output-folder "output/path" \
  --template "PMOD-VOI/AAL-MERGED-NEURO.nii.gz" \
  --gm-template "map/gm_prob_map.nii.gz" \
  --csf-template "map/csf_prob_map.nii.gz" \
  --wm-template "map/wm_prob_map.nii.gz" \
  --aal-txt "PMOD-VOI/AAL-MERGED-NEURO.txt" \
  --wm-threshold 0.5 \
  --gm-threshold 0.5 \
  --csf-threshold 0.5 \
  --gm-mask-noncortical false \
  --use-csf-prob true \
  --output-dicom true \
  --dicom-series-desc "Processed AAL Template"
```

## 参数说明


#### DICOM相关参数
- `--moving-series-desc`: 移动图像DICOM序列描述过滤条件
- `--fixed-series-desc`: 固定图像DICOM序列描述过滤条件
- `--auto-convert-dicom`: 自动转换DICOM序列（默认启用）
- `--dicom-output-subfolder`: DICOM转换输出子文件夹名称（默认: converted）

#### 输出DICOM参数
- `--output-dicom`: 将最终结果转换为DICOM格式（默认启用）
- `--dicom-series-desc`: 输出DICOM的序列描述（默认: Processed AAL Template）
- `--dicom-output-folder`: DICOM输出文件夹，默认在output-folder下创建dicom子文件夹

#### CSF选项参数
- `--use-csf-prob`: 是否使用CSF概率图谱（默认: True）

### 前端输入参数（需要用户设置）

1. **概率阈值参数**：
   - `--wm-threshold`: 白质概率阈值，范围0-1
   - `--gm-threshold`: 灰质概率阈值，范围0-1
   - `--csf-threshold`: 脑脊液概率阈值，范围0-1

2. **处理参数**：
   - `--gm-mask-noncortical`: 是否对非皮层区域应用GM掩码
   - `--use-csf-prob`: 是否使用CSF概率图谱

### 内置参数（系统默认）

1. **路径参数**：
   - `--template`: AAL模板路径
   - `--gm-template`: GM概率图路径
   - `--csf-template`: CSF概率图路径
   - `--wm-template`: WM概率图路径
   - `--aal-txt`: AAL标签文件路径

2. **配准参数**：
   - `--histogram-bins`: 直方图箱数
   - `--learning-rate`: 学习率
   - `--min-step`: 最小步长
   - `--iterations`: 迭代次数
   - `--relaxation-factor`: 松弛因子

## 工作流程

### 1. 输入处理
```
输入路径 → 检测格式 → DICOM转换（如需要） → NIFTI文件
```

### 2. 主要处理流程
```
仿射配准 → 模板变换 → 概率图相交 → 白质补全
```

### 3. 输出处理
```
NIFTI结果 → DICOM转换（如需要） → 最终输出
```

## CSF选项的影响

### 使用CSF概率图谱（use_csf_prob=true）
- 读取CSF概率图
- 应用CSF阈值掩码
- 最终掩码 = GM掩码 × (非CSF掩码)

### 不使用CSF概率图谱（use_csf_prob=false）
- 跳过CSF概率图读取
- 创建全零CSF图像作为占位符
- 最终掩码 = GM掩码

## 输出文件

### NIFTI输出
- `Centiloid.nii.gz`: 相交后的AAL模板
- `Centiloid_withWM.nii.gz`: 补全白质后的最终结果

### DICOM输出（如果启用）
- `Centiloid_dicom/`: 相交后AAL模板的DICOM序列
- `Centiloid_withWM_dicom/`: 补全白质后结果的DICOM序列

## 注意事项

1. **DICOM支持**：
   - 支持常见的DICOM文件扩展名（.dcm, .DCM, .dicom, .DICOM）
   - 自动检测DICOM系列并选择第一个系列
   - 可以通过序列描述过滤特定系列

2. **CSF选项**：
   - 当不使用CSF时，处理速度可能更快
   - 某些情况下可能获得不同的分割结果
   - 建议根据具体数据特点选择是否使用CSF

3. **内存使用**：
   - DICOM转换可能增加内存使用
   - 大图像处理时注意系统资源

4. **文件路径**：
   - 确保所有输入文件路径正确
   - DICOM文件夹应包含完整的DICOM序列 