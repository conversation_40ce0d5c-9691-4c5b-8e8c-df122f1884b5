#!/usr/bin/env python3
"""
简单测试GPU豪斯多夫距离计算
"""

import SimpleITK as sitk
import numpy as np
import time

# 导入我们的函数
from contact import calculate_segmentation_metrics_manual

def simple_test():
    """简单测试"""
    
    # 文件路径
    file1_path = 'output/pmod_result/N30R83_1MM_2/parcel_s2_Brain_segments__Hammers_N30R83_1mm_atlas_region_labels____NORMALIZED_to_TOF_AC_SC_GF_MF_1_0____Atlas_in_Functional_space_Hammers_N30R83_1MM.nii'
    file2_path = 'output/my_result/Hammers-N30R83-1MM/Hammers-N30R83-1MM_withWM.nii.gz'
    
    print("简单测试GPU豪斯多夫距离计算")
    
    try:
        print("开始GPU计算...")
        results = calculate_segmentation_metrics_manual(
            file1_path, file2_path, 
            label_range=(1, 130), 
            use_gpu=True
        )
        
        if results:
            print("✅ 计算成功!")
            print(f"Dice: {results['Dice']:.4f}")
            print(f"Hausdorff: {results['Hausdorff']:.4f}")
        else:
            print("❌ 计算失败")
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    simple_test()
