#!/usr/bin/env python3
"""
Test script to verify CuPy installation and CUDA compatibility
"""

def test_cupy_installation():
    try:
        import cupy as cp
        print("✅ CuPy imported successfully")
        print(f"CuPy version: {cp.__version__}")
        
        # Test CUDA runtime
        try:
            cuda_version = cp.cuda.runtime.runtimeGetVersion()
            print(f"CUDA runtime version: {cuda_version}")
        except Exception as e:
            print(f"❌ CUDA runtime error: {e}")
            return False
        
        # Test GPU device
        try:
            device_count = cp.cuda.runtime.getDeviceCount()
            print(f"Number of CUDA devices: {device_count}")
            
            if device_count > 0:
                device_props = cp.cuda.runtime.getDeviceProperties(0)
                print(f"GPU 0: {device_props['name'].decode()}")
        except Exception as e:
            print(f"❌ GPU device error: {e}")
            return False
        
        # Test basic array operations
        try:
            x = cp.array([1, 2, 3, 4, 5])
            y = x * 2
            result = cp.asnumpy(y)
            print(f"✅ Basic GPU computation test: {result}")
        except Exception as e:
            print(f"❌ GPU computation error: {e}")
            return False
        
        print("✅ All CuPy tests passed!")
        return True
        
    except ImportError as e:
        print(f"❌ CuPy import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    test_cupy_installation()
