#!/usr/bin/env python3
"""
测试豪斯多夫距离计算的诊断脚本
"""

import SimpleITK as sitk
import numpy as np
import os

def test_hausdorff_calculation():
    """测试豪斯多夫距离计算"""
    
    # 文件路径
    file1_path = 'output/pmod_result/N30R83_1MM_2/parcel_s2_Brain_segments__Hammers_N30R83_1mm_atlas_region_labels____NORMALIZED_to_TOF_AC_SC_GF_MF_1_0____Atlas_in_Functional_space_Hammers_N30R83_1MM.nii'
    file2_path = 'output/my_result/Hammers-N30R83-1MM/Hammers-N30R83-1MM_withWM.nii.gz'
    
    print("=== 豪斯多夫距离计算诊断 ===\n")
    
    # 检查文件是否存在
    if not os.path.exists(file1_path):
        print(f"❌ 金标准文件不存在: {file1_path}")
        return
    if not os.path.exists(file2_path):
        print(f"❌ 分割结果文件不存在: {file2_path}")
        return
    
    print("✅ 文件存在检查通过")
    
    try:
        # 读取图像
        print("\n1. 读取图像...")
        gt_img = sitk.ReadImage(file1_path, sitk.sitkUInt16)
        seg_img = sitk.ReadImage(file2_path, sitk.sitkUInt16)
        print(f"   金标准图像尺寸: {gt_img.GetSize()}")
        print(f"   分割结果图像尺寸: {seg_img.GetSize()}")
        
        # 检查空间信息匹配
        print("\n2. 检查空间信息匹配...")
        metadata_mismatch = any([
            gt_img.GetSize() != seg_img.GetSize(),
            gt_img.GetOrigin() != seg_img.GetOrigin(),
            gt_img.GetSpacing() != seg_img.GetSpacing(),
            gt_img.GetDirection() != seg_img.GetDirection()
        ])
        
        if metadata_mismatch:
            print("   ⚠️  空间信息不匹配，需要重采样")
            resampler = sitk.ResampleImageFilter()
            resampler.SetReferenceImage(gt_img)
            resampler.SetInterpolator(sitk.sitkNearestNeighbor)
            resampler.SetDefaultPixelValue(0)
            seg_img = resampler.Execute(seg_img)
            print("   ✅ 重采样完成")
        else:
            print("   ✅ 空间信息匹配")
        
        # 测试整体前景豪斯多夫距离
        print("\n3. 测试整体前景豪斯多夫距离...")
        label_range = (1, 130)
        gt_binary = sitk.BinaryThreshold(gt_img, lowerThreshold=label_range[0], 
                                       upperThreshold=label_range[1], 
                                       insideValue=1, outsideValue=0)
        seg_binary = sitk.BinaryThreshold(seg_img, lowerThreshold=label_range[0], 
                                        upperThreshold=label_range[1], 
                                        insideValue=1, outsideValue=0)
        
        # 检查二值图像是否有内容
        gt_stats = sitk.StatisticsImageFilter()
        gt_stats.Execute(gt_binary)
        seg_stats = sitk.StatisticsImageFilter()
        seg_stats.Execute(seg_binary)
        
        print(f"   金标准前景体素数: {int(gt_stats.GetSum())}")
        print(f"   分割结果前景体素数: {int(seg_stats.GetSum())}")
        
        if gt_stats.GetSum() == 0:
            print("   ❌ 金标准前景为空！")
            return
        if seg_stats.GetSum() == 0:
            print("   ❌ 分割结果前景为空！")
            return
        
        try:
            hausdorff_filter = sitk.HausdorffDistanceImageFilter()
            hausdorff_filter.Execute(gt_binary, seg_binary)
            hausdorff_distance = hausdorff_filter.GetHausdorffDistance()
            print(f"   ✅ 整体豪斯多夫距离: {hausdorff_distance:.4f}")
        except Exception as e:
            print(f"   ❌ 整体豪斯多夫距离计算失败: {e}")
        
        # 测试单个标签的豪斯多夫距离
        print("\n4. 测试单个标签豪斯多夫距离...")
        
        # 获取金标准中存在的标签
        gt_array = sitk.GetArrayFromImage(gt_img)
        unique_labels = np.unique(gt_array)
        test_labels = [l for l in unique_labels if label_range[0] <= l <= label_range[1] and l != 0][:5]  # 测试前5个标签
        
        print(f"   测试标签: {test_labels}")
        
        for label in test_labels:
            print(f"\n   测试标签 {label}:")
            
            # 创建单标签二值图像
            gt_label = sitk.BinaryThreshold(gt_img, lowerThreshold=float(label),
                                          upperThreshold=float(label),
                                          insideValue=1, outsideValue=0)
            seg_label = sitk.BinaryThreshold(seg_img, lowerThreshold=float(label),
                                           upperThreshold=float(label),
                                           insideValue=1, outsideValue=0)
            
            # 检查标签是否存在
            gt_label_stats = sitk.StatisticsImageFilter()
            gt_label_stats.Execute(gt_label)
            seg_label_stats = sitk.StatisticsImageFilter()
            seg_label_stats.Execute(seg_label)
            
            gt_voxels = int(gt_label_stats.GetSum())
            seg_voxels = int(seg_label_stats.GetSum())
            
            print(f"     金标准体素数: {gt_voxels}")
            print(f"     分割结果体素数: {seg_voxels}")
            
            if gt_voxels == 0:
                print(f"     ❌ 标签 {label} 在金标准中不存在")
                continue
            if seg_voxels == 0:
                print(f"     ❌ 标签 {label} 在分割结果中不存在")
                continue
            
            try:
                hausdorff_filter = sitk.HausdorffDistanceImageFilter()
                hausdorff_filter.Execute(gt_label, seg_label)
                hausdorff_distance = hausdorff_filter.GetHausdorffDistance()
                print(f"     ✅ 豪斯多夫距离: {hausdorff_distance:.4f}")
            except Exception as e:
                print(f"     ❌ 豪斯多夫距离计算失败: {e}")
                print(f"     错误类型: {type(e).__name__}")
                
                # 尝试更详细的诊断
                try:
                    # 检查图像是否有效
                    print(f"     调试信息:")
                    print(f"       GT标签图像尺寸: {gt_label.GetSize()}")
                    print(f"       Seg标签图像尺寸: {seg_label.GetSize()}")
                    print(f"       GT标签图像像素类型: {gt_label.GetPixelIDTypeAsString()}")
                    print(f"       Seg标签图像像素类型: {seg_label.GetPixelIDTypeAsString()}")
                    
                    # 检查是否有连通区域
                    gt_cc = sitk.ConnectedComponent(gt_label)
                    seg_cc = sitk.ConnectedComponent(seg_label)
                    gt_cc_stats = sitk.StatisticsImageFilter()
                    gt_cc_stats.Execute(gt_cc)
                    seg_cc_stats = sitk.StatisticsImageFilter()
                    seg_cc_stats.Execute(seg_cc)
                    print(f"       GT连通区域数: {int(gt_cc_stats.GetMaximum())}")
                    print(f"       Seg连通区域数: {int(seg_cc_stats.GetMaximum())}")
                    
                except Exception as debug_e:
                    print(f"     调试信息获取失败: {debug_e}")
        
        print("\n=== 诊断完成 ===")
        
    except Exception as e:
        print(f"❌ 总体错误: {e}")
        print(f"错误类型: {type(e).__name__}")

if __name__ == "__main__":
    test_hausdorff_calculation()
