import os
import numpy as np
from datetime import datetime
from pydicom.dataset import Dataset, FileDataset
from pydicom.uid import generate_uid
import SimpleITK as sitk
SITK_AVAILABLE = True
import shutil

def nifti_to_dicom_sitk(nifti_path, output_dir, series_description="Converted from NIfTI"):
    """
    使用SimpleITK将NIfTI转换为DICOM (推荐方法)

    Args:
        nifti_path (str): NIfTI文件路径
        output_dir (str): 输出目录
        series_description (str): DICOM系列描述
    """
    if not SITK_AVAILABLE:
        raise ImportError("SimpleITK 未安装")

    # 读取NIfTI文件
    #print(f"正在读取NIfTI文件: {nifti_path}")
    image = sitk.ReadImage(nifti_path)
    return image_to_dicom_sitk(image,output_dir, series_description)



def image_to_dicom_sitk(image,output_dir, series_description="Converted from NIfTI"):
    # 获取图像信息
    size = image.GetSize()
    spacing = image.GetSpacing()
    origin = image.GetOrigin()
    direction = image.GetDirection()

    # print(f"图像尺寸: {size}")
    # print(f"像素间距: {spacing}")
    # print(f"原点: {origin}")

    # 创建输出目录
    if os.path.exists(output_dir):
        # 删除整个文件夹及其内容
        shutil.rmtree(output_dir)
        # 重新创建同名空文件夹
    os.makedirs(output_dir)
    # 生成唯一ID
    series_uid = generate_uid()
    study_uid = generate_uid()
    frame_of_reference_uid = generate_uid()

    # 获取当前时间
    dt = datetime.now()
    date_str = dt.strftime("%Y%m%d")
    time_str = dt.strftime("%H%M%S.%f")[:-3]

    # 转换每个切片
    num_slices = size[2] if len(size) > 2 else 1

    for i in range(num_slices):
        # 提取单个切片
        if len(size) > 2:
            slice_filter = sitk.ExtractImageFilter()
            slice_filter.SetSize([size[0], size[1], 0])
            slice_filter.SetIndex([0, 0, i])
            slice_image = slice_filter.Execute(image)
        else:
            slice_image = image

        # 转换为numpy数组
        array = sitk.GetArrayFromImage(slice_image)

        # 创建DICOM数据集
        file_meta = Dataset()
        file_meta.MediaStorageSOPClassUID = '1.2.840.10008.*******.1.7'  # Secondary Capture Image Storage
        file_meta.MediaStorageSOPInstanceUID = generate_uid()
        file_meta.ImplementationClassUID = generate_uid()
        file_meta.TransferSyntaxUID = '1.2.840.10008.1.2'  # Implicit VR Little Endian

        ds = FileDataset(f"slice_{i:04d}.dcm", {}, file_meta=file_meta, preamble=b"\0" * 128)

        # 设置必要的DICOM标签
        ds.PatientName = "Anonymous"
        ds.PatientID = "ANON001"
        ds.PatientBirthDate = ""
        ds.PatientSex = ""

        ds.StudyDate = date_str
        ds.StudyTime = time_str
        ds.StudyInstanceUID = study_uid
        ds.StudyID = "1"
        ds.StudyDescription = "NIfTI Conversion"

        ds.SeriesDate = date_str
        ds.SeriesTime = time_str
        ds.SeriesInstanceUID = series_uid
        ds.SeriesNumber = 1
        ds.SeriesDescription = series_description

        ds.InstanceNumber = i + 1
        ds.SOPInstanceUID = generate_uid()
        ds.SOPClassUID = '1.2.840.10008.*******.1.7'

        ds.Modality = "OT"  # Other
        ds.FrameOfReferenceUID = frame_of_reference_uid

        # 图像相关信息
        ds.ImageType = ["DERIVED", "SECONDARY"]
        ds.SamplesPerPixel = 1
        ds.PhotometricInterpretation = "MONOCHROME2"
        ds.Rows = array.shape[0]
        ds.Columns = array.shape[1]
        ds.BitsAllocated = 16
        ds.BitsStored = 16
        ds.HighBit = 15
        ds.PixelRepresentation = 0

        # 空间信息
        if len(spacing) >= 2:
            ds.PixelSpacing = [spacing[1], spacing[0]]  # 行间距, 列间距
        if len(spacing) >= 3:
            ds.SliceThickness = spacing[2]

        # 位置信息
        if len(size) > 2:
            slice_location = origin[2] + i * spacing[2]
            ds.SliceLocation = slice_location

            # 图像位置和方向
            ds.ImagePositionPatient = [origin[0], origin[1], slice_location]
            ds.ImageOrientationPatient = [direction[0], direction[1], direction[2],
                                          direction[3], direction[4], direction[5]]

        # 转换像素数据
        array = array.astype(np.uint16)
        ds.PixelData = array.tobytes()

        # 保存DICOM文件
        output_filename = os.path.join(output_dir, f"slice_{i:04d}.dcm")
        ds.save_as(output_filename, write_like_original=False)
        #print(f"已保存切片 {i + 1}/{num_slices}: {output_filename}")

    print(f"{output_dir}:转换完成！共生成 {num_slices} 个DICOM文件")
    return num_slices


def read_dicom_series(directory_path):
    """
    使用 SimpleITK 读取一个包含 DICOM 文件的目录，并返回一个三维图像对象。

    Args:
        directory_path (str): 包含 DICOM 文件的目录路径。

    Returns:
        sitk.Image: 组织好的三维图像对象。
    """
    # 1. 确保目录存在
    if not os.path.isdir(directory_path):
        print(f"错误: 目录不存在: {directory_path}")
        return None

    # 2. 获取目录中所有 DICOM 文件的文件名
    # 使用推荐的 GetGDCMSeriesFileNames 方法
    try:
        series_IDs = sitk.ImageSeriesReader.GetGDCMSeriesFileNames(directory_path)
    except RuntimeError as e:
        print(f"读取DICOM序列失败: {e}")
        print("请检查目录中是否包含有效的DICOM文件。")
        return None

    if not series_IDs:
        print(f"在目录 {directory_path} 中没有找到DICOM文件。")
        return None

    # 3. 创建一个 ImageSeriesReader 对象
    reader = sitk.ImageSeriesReader()

    # 4. 设置读取器的文件名列表
    reader.SetFileNames(series_IDs)

    # 5. 执行读取操作
    print(f"正在读取 {len(series_IDs)} 个 DICOM 文件...")
    image = reader.Execute()

    print("DICOM 序列已成功读取。")
    return image


# --- 使用示例 ---
if __name__ == '__main__':
    # 假设你有一个名为 'example.nii.gz' 的NII文件
    # 请确保该文件存在于你的工作目录中
    nii_file_path = 'D:/Work/Brain/li_yunqiao_PET/NIFTI_Output_Folder/Series.nii.gz'
    output_directory = 'D:/Work/Brain/li_yunqiao_PET/NIFTI_Output_Folder/Series'
    nifti_to_dicom_sitk(nii_file_path,output_directory)