import SimpleITK as sitk
import os

def transform_template_to_pet(template_path, pet_path, affine_path, output_inverse_path, interp_mode="linear", output_type=None):
    """
    将模板/概率图通过仿射逆变换映射到PET空间。
    interp_mode: "linear"（概率图）或"nearest"（标签图）
    output_type: None（保持原类型），或sitk.sitkInt32等
    """
    template_img = sitk.ReadImage(template_path, sitk.sitkFloat32)
    pet_img = sitk.ReadImage(pet_path, sitk.sitkFloat32)
    affine = sitk.ReadTransform(affine_path)
    affine_inv = affine.GetInverse()
    resampler = sitk.ResampleImageFilter()
    resampler.SetReferenceImage(pet_img)
    if interp_mode == "nearest":
        resampler.SetInterpolator(sitk.sitkNearestNeighbor)
    else:
        resampler.SetInterpolator(sitk.sitkLinear)
    resampler.SetDefaultPixelValue(0)
    resampler.SetTransform(affine_inv)
    template_in_pet_space = resampler.Execute(template_img)
    if output_type is not None:
        template_in_pet_space = sitk.Cast(template_in_pet_space, output_type)
    sitk.WriteImage(template_in_pet_space, output_inverse_path)
    return output_inverse_path 