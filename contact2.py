import SimpleITK as sitk
import numpy as np

def calculate_segmentation_metrics_manual(ground_truth_path, segmentation_path, label_range=(1, 130)):
    """
    计算并返回两个3D标签nii文件之间的分割评估指标。
    - 使用NumPy手动计算指标，以兼容非常旧的SimpleITK版本。
    - 自动将多标签合并为二值化前景。
    - 自动处理物理空间不匹配的问题，将待评估图像重采样到金标准空间。
    """
    
    try:
        gt_img_multi_label = sitk.ReadImage(ground_truth_path, sitk.sitkUInt8)
        seg_img_multi_label = sitk.ReadImage(segmentation_path, sitk.sitkUInt8)
    except Exception as e:
        print(f"错误：无法读取文件。请检查路径是否正确。 {e}")
        return None

    # 检查元数据并根据需要进行重采样
    metadata_mismatch = any([
        gt_img_multi_label.GetSize() != seg_img_multi_label.GetSize(),
        gt_img_multi_label.GetOrigin() != seg_img_multi_label.GetOrigin(),
        gt_img_multi_label.GetSpacing() != seg_img_multi_label.GetSpacing(),
        gt_img_multi_label.GetDirection() != seg_img_multi_label.GetDirection()
    ])

    seg_img_to_process = seg_img_multi_label
    if metadata_mismatch:
        print("警告：检测到元数据不匹配。正在重采样分割结果...")
        resampler = sitk.ResampleImageFilter()
        resampler.SetReferenceImage(gt_img_multi_label)
        resampler.SetInterpolator(sitk.sitkNearestNeighbor)
        resampler.SetDefaultPixelValue(0)
        seg_img_to_process = resampler.Execute(seg_img_multi_label)
        print("重采样完成。")

    # 将多标签图像二值化
    lower_bound, upper_bound = label_range
    inside_value = 1
    outside_value = 0
    gt_img_binary = sitk.BinaryThreshold(gt_img_multi_label, lowerThreshold=lower_bound, upperThreshold=upper_bound, insideValue=inside_value, outsideValue=outside_value)
    seg_img_binary = sitk.BinaryThreshold(seg_img_to_process, lowerThreshold=lower_bound, upperThreshold=upper_bound, insideValue=inside_value, outsideValue=outside_value)

    # --- 核心修改：使用NumPy手动计算指标 ---
    # 将SimpleITK图像转换为NumPy数组以便进行计算
    gt_np = sitk.GetArrayFromImage(gt_img_binary)
    seg_np = sitk.GetArrayFromImage(seg_img_binary)

    # 确保数组是布尔类型以便进行逻辑运算
    gt_bool = gt_np.astype(bool)
    seg_bool = seg_np.astype(bool)

    # 计算真阳性、假阳性、假阴性
    tp = np.sum(gt_bool & seg_bool)  # 真阳性：两者都为True的像素
    fp = np.sum(~gt_bool & seg_bool) # 假阳性：金标准为False，但分割结果为True
    fn = np.sum(gt_bool & ~seg_bool) # 假阴性：金标准为True，但分割结果为False
    
    # 避免除以零
    dice = (2 * tp) / (2 * tp + fp + fn) if (2 * tp + fp + fn) > 0 else 0.0
    jaccard = tp / (tp + fp + fn) if (tp + fp + fn) > 0 else 0.0
    precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
    sensitivity = tp / (tp + fn) if (tp + fn) > 0 else 0.0
    # --- NumPy手动计算结束 ---
    
    # Hausdorff距离仍然使用SimpleITK计算
    try:
        hausdorff_distance_filter = sitk.HausdorffDistanceImageFilter()
        hausdorff_distance_filter.Execute(gt_img_binary, seg_img_binary)
        hausdorff = hausdorff_distance_filter.GetHausdorffDistance()
    except Exception as e:
        print(f"计算Hausdorff距离时出错: {e}")
        hausdorff = -1.0 # 使用一个无效值表示计算失败

    metrics = {
        'Dice': dice,
        'Jaccard': jaccard,
        'Precision': precision,
        'Sensitivity': sensitivity,
        'Hausdorff': hausdorff
    }
    
    return metrics

if __name__ == '__main__':
    file1_path = 'output/pmod_result/N30R83_1MM_2/parcel_s2_Brain_segments__Hammers_N30R83_1mm_atlas_region_labels____NORMALIZED_to_TOF_AC_SC_GF_MF_1_0____Atlas_in_Functional_space_Hammers_N30R83_1MM.nii'
    file2_path = 'output/my_result/Hammers-N30R83-1MM/Hammers-N30R83-1MM_withWM.nii.gz'
    label_range_to_evaluate = (1, 130)

    print(f"正在比较:\n  - 金标准 (GT): {file1_path}\n  - 分割结果 (Seg): {file2_path}\n")
    
    try:
        # 注意函数名已更改为 ..._manual
        results = calculate_segmentation_metrics_manual(file1_path, file2_path, label_range=label_range_to_evaluate)

        if results:
            print("\n--- 计算结果 (手动兼容模式) ---")
            print(f"评估的标签范围: {label_range_to_evaluate}")
            print(f"'Dice'      : {results['Dice']:.4f}")
            print(f"'Jaccard'   : {results['Jaccard']:.4f}")
            print(f"'Precision' : {results['Precision']:.4f}")
            print(f"'Sensitivity': {results['Sensitivity']:.4f}")
            print(f"'Hausdorff' : {results['Hausdorff']:.4f}")
            print("---------------------------------")

    except Exception as e:
        print(f"\n在处理你的文件时发生严重错误: {e}")
        print("请检查文件路径和文件本身是否有效。")