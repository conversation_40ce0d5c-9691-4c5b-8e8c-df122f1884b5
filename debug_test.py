#!/usr/bin/env python3
"""
调试GPU豪斯多夫距离计算
"""

import SimpleITK as sitk
import numpy as np

def debug_test():
    """调试测试"""
    
    print("开始调试测试...")
    
    try:
        # 检查CuPy
        import cupy as cp
        print("✅ CuPy可用")
        
        # 检查文件
        file1_path = 'output/pmod_result/N30R83_1MM_2/parcel_s2_Brain_segments__Hammers_N30R83_1mm_atlas_region_labels____NORMALIZED_to_TOF_AC_SC_GF_MF_1_0____Atlas_in_Functional_space_Hammers_N30R83_1MM.nii'
        file2_path = 'output/my_result/Hammers-N30R83-1MM/Hammers-N30R83-1MM_withWM.nii.gz'
        
        print("读取图像...")
        gt_img = sitk.ReadImage(file1_path, sitk.sitkUInt8)
        seg_img = sitk.ReadImage(file2_path, sitk.sitkUInt8)
        print("✅ 图像读取成功")
        
        # 二值化
        print("二值化...")
        gt_binary = sitk.BinaryThreshold(gt_img, lowerThreshold=1, upperThreshold=130, insideValue=1, outsideValue=0)
        seg_binary = sitk.BinaryThreshold(seg_img, lowerThreshold=1, upperThreshold=130, insideValue=1, outsideValue=0)
        print("✅ 二值化成功")
        
        # 转换为numpy
        print("转换为numpy...")
        gt_np = sitk.GetArrayFromImage(gt_binary).astype(bool)
        seg_np = sitk.GetArrayFromImage(seg_binary).astype(bool)
        print(f"✅ 转换成功，形状: {gt_np.shape}")
        
        # 检查前景
        gt_sum = np.sum(gt_np)
        seg_sum = np.sum(seg_np)
        print(f"GT前景体素: {gt_sum}")
        print(f"Seg前景体素: {seg_sum}")
        
        if gt_sum == 0 or seg_sum == 0:
            print("❌ 前景为空")
            return
        
        # 测试SimpleITK豪斯多夫距离
        print("测试SimpleITK豪斯多夫距离...")
        hausdorff_filter = sitk.HausdorffDistanceImageFilter()
        hausdorff_filter.Execute(gt_binary, seg_binary)
        sitk_hausdorff = hausdorff_filter.GetHausdorffDistance()
        print(f"✅ SimpleITK豪斯多夫距离: {sitk_hausdorff:.4f}")
        
        # 测试我们的CPU实现
        print("测试我们的CPU实现...")
        from contact import _compute_hausdorff_cpu_fallback
        spacing = gt_img.GetSpacing()
        cpu_hausdorff = _compute_hausdorff_cpu_fallback(gt_np, seg_np, spacing)
        print(f"✅ CPU实现豪斯多夫距离: {cpu_hausdorff:.4f}")
        
        # 测试GPU实现
        print("测试GPU实现...")
        from contact import _compute_hausdorff_gpu
        gt_gpu = cp.asarray(gt_np)
        seg_gpu = cp.asarray(seg_np)
        gpu_hausdorff = _compute_hausdorff_gpu(gt_gpu, seg_gpu, spacing, cp)
        print(f"✅ GPU实现豪斯多夫距离: {gpu_hausdorff:.4f}")
        
        print("✅ 所有测试完成")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_test()
