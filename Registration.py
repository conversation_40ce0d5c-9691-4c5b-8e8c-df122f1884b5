import SimpleITK as sitk
import os
import sys

# 设置全局随机种子，保证配准结果可复现
# sitk.ImageRegistrationMethod.SetGlobalDefaultRandomSeed(42)

# --- 步骤 1: 设置文件路径 ---
moving_image_path = "li_yunqiao_PET/NIFTI_Output_Folder/Series.nii.gz"
fixed_image_path = "normalization/PET.nii.gz"
output_folder_path = "output/my_result/AAL-VOIs"

# --- 参数设置 ---
# 极保守的B样条设置，网格非常粗糙以防止过度配准
BSPLINE_GRID_SIZE = 5 # 最小网格密度，类似SPM的最保守设置

# --- 脚本主体 ---
if not all(os.path.exists(p) for p in [moving_image_path, fixed_image_path]):
    print("错误: 找不到输入的数据文件或模板文件。请仔细检查路径。")
    sys.exit(1)
if not os.path.isdir(output_folder_path):
    print(f"输出文件夹不存在，正在创建: {output_folder_path}")
    os.makedirs(output_folder_path)

print("--- 开始标准三步法图像配准流程 (仿照SPM风格) ---")
fixed_image = sitk.ReadImage(fixed_image_path, sitk.sitkFloat32)
moving_image = sitk.ReadImage(moving_image_path, sitk.sitkFloat32)

# PET定量保护配准策略：创建归一化副本用于配准，保持原始图像不变
print("正在进行PET定量保护的预处理...")

# 保存原始图像（保持定量数值）
original_fixed_image = fixed_image
original_moving_image = moving_image

def create_registration_copy(image, image_name):
    """创建用于配准的图像副本，仅用于配准算法，不影响最终定量结果"""
    stats = sitk.StatisticsImageFilter()
    stats.Execute(image)
    mean_val = stats.GetMean()
    max_val = stats.GetMaximum()
    
    print(f"  {image_name} 原始强度 - 均值: {mean_val:.2f}, 最大值: {max_val:.2f}")
    
    # 创建归一化副本仅用于配准优化
    if mean_val > 0:
        # 轻度归一化，避免过度改变强度分布
        normalized = sitk.Divide(image, mean_val / 50.0)
        stats.Execute(normalized)
        print(f"  {image_name} 配准副本 - 均值: {stats.GetMean():.2f}, 最大值: {stats.GetMaximum():.2f}")
        return normalized
    return image

# 创建用于配准的归一化副本
fixed_image = create_registration_copy(original_fixed_image, "固定图像")
moving_image = create_registration_copy(original_moving_image, "移动图像")

print("✓ 配准副本已创建，原始定量数值得到保护")

def registration_progress_update(registration_method, stage_name):
    current_iter = registration_method.GetOptimizerIteration()
    metric_value = registration_method.GetMetricValue()
    stop_condition = registration_method.GetOptimizerStopConditionDescription()
    print(f"阶段: {stage_name} | 迭代: {current_iter:3} | 度量值: {metric_value:10.5f} | 停止条件: {stop_condition}      ", end='\r')

# --- 仿射配准 ---
print("\n--- 仿射配准 (处理全局对齐) ---")
affine_registration = sitk.ImageRegistrationMethod()
affine_registration.SetMetricAsMattesMutualInformation(numberOfHistogramBins=50)
affine_registration.SetMetricSamplingStrategy(affine_registration.NONE)
affine_registration.SetInterpolator(sitk.sitkLinear)
affine_registration.SetOptimizerAsRegularStepGradientDescent(learningRate=2.0, minStep=1e-6, numberOfIterations=300, relaxationFactor=0.8)
affine_registration.SetOptimizerScalesFromPhysicalShift()
affine_registration.SetShrinkFactorsPerLevel([8, 4, 2, 1])
affine_registration.SetSmoothingSigmasPerLevel([4, 2, 1, 0])
affine_registration.SmoothingSigmasAreSpecifiedInPhysicalUnitsOn()
initial_transform = sitk.CenteredTransformInitializer(fixed_image, moving_image, sitk.AffineTransform(3), sitk.CenteredTransformInitializerFilter.MOMENTS)
affine_registration.SetInitialTransform(initial_transform)
affine_registration.AddCommand(sitk.sitkIterationEvent, lambda: registration_progress_update(affine_registration, "仿射"))
final_affine_transform = affine_registration.Execute(fixed_image, moving_image)
sitk.WriteTransform(final_affine_transform, os.path.join(output_folder_path, "final_affine_transform.tfm"))
print(f"仿射变换已保存至: {os.path.join(output_folder_path, 'final_affine_transform.tfm')}")
print(f"\n仿射配准完成。")
print(f"最终度量值: {affine_registration.GetMetricValue():.6f}")
print(f"优化器停止原因: {affine_registration.GetOptimizerStopConditionDescription()}")
affine_registration.RemoveAllCommands()

# --- 应用仿射变换，生成对齐图像 ---
print("\n--- 应用仿射变换，生成对齐图像 ---")
moving_image_affined_original = sitk.Resample(original_moving_image, original_fixed_image, final_affine_transform, sitk.sitkLinear, 0.0, original_moving_image.GetPixelID())
sitk.WriteImage(moving_image_affined_original, os.path.join(output_folder_path, "w_pet_affine_only.nii.gz"))
print(f"仿射对齐的图像已保存至: {os.path.join(output_folder_path, 'w_pet_affine_only.nii.gz')}")

# ================== 以下为B样条配准及后续流程（已注释，可随时恢复） ==================
'''
# --- 第三阶段: B-样条可变形配准 ---
print("\n--- 第三阶段: B-样条可变形配准 (处理局部形变) ---")
bspline_registration = sitk.ImageRegistrationMethod()
bspline_registration.SetMetricAsMattesMutualInformation(numberOfHistogramBins=50)
bspline_registration.SetMetricSamplingStrategy(bspline_registration.RANDOM)
bspline_registration.SetMetricSamplingPercentage(0.05)
bspline_registration.SetInterpolator(sitk.sitkLinear)
bspline_registration.SetOptimizerAsLBFGSB(
    gradientConvergenceTolerance=1e-4,  # 放宽收敛条件
    numberOfIterations=20,  # 大幅减少迭代次数
    maximumNumberOfCorrections=3, 
    maximumNumberOfFunctionEvaluations=100  # 严格限制函数评估次数
)
bspline_registration.SetShrinkFactorsPerLevel([2, 1])
bspline_registration.SetSmoothingSigmasPerLevel([1, 0])
bspline_registration.SmoothingSigmasAreSpecifiedInPhysicalUnitsOn()
bspline_transform = sitk.BSplineTransformInitializer(fixed_image, [BSPLINE_GRID_SIZE] * fixed_image.GetDimension())
bspline_registration.SetInitialTransform(bspline_transform, inPlace=True)
bspline_registration.AddCommand(sitk.sitkIterationEvent, lambda: registration_progress_update(bspline_registration, "B样条"))
final_bspline_transform = bspline_registration.Execute(fixed_image, moving_image_affined)
sitk.WriteTransform(final_bspline_transform, os.path.join(output_folder_path, "final_bspline_transform.tfm"))
print(f"B样条变换已保存至: {os.path.join(output_folder_path, 'final_bspline_transform.tfm')}")
print(f"\nB-样条配准完成。")

# 验证B样条变形的合理性
print("\n验证B样条变形场...")
def validate_bspline_transform(bspline_transform):
    import numpy as np
    params = bspline_transform.GetParameters()
    params_array = np.array(params)
    max_deformation = max(abs(params_array.max()), abs(params_array.min()))
    mean_deformation = np.abs(params_array).mean()
    print(f"  B样条参数范围: [{params_array.min():.6f}, {params_array.max():.6f}]")
    print(f"  最大变形: {max_deformation:.6f}mm, 平均变形: {mean_deformation:.6f}mm")
    if max_deformation > 20:
        print("  [警告] 变形可能过大，建议检查配准参数！")
    return max_deformation < 20
is_bspline_valid = validate_bspline_transform(final_bspline_transform)
bspline_registration.RemoveAllCommands()

# --- 第四阶段: 智能变换选择和结果生成 ---
print("\n--- 第四阶段: 智能变换选择并生成所有输出文件 ---")
if not is_bspline_valid:
    print("⚠ 检测到B样条过度配准，回退到仅使用仿射配准...")
    final_transform = final_affine_transform
    use_bspline = False
else:
    print("✓ B样条配准正常，使用完整的复合变换...")
    final_transform = sitk.CompositeTransform([final_affine_transform, final_bspline_transform])
    use_bspline = True
sitk.WriteTransform(final_transform, os.path.join(output_folder_path, "final_composite_transform.tfm"))
print(f"最终复合变换已保存至: {os.path.join(output_folder_path, 'final_composite_transform.tfm')}")

# --- 诊断和生成所有文件 ---
# ...（后续调制、雅可比、质量评估等代码同原脚本）...
'''
# ================== 以上为B样条配准及后续流程（已注释） ==================

# --- 诊断输出 ---
def print_stats(image, name):
    stats = sitk.StatisticsImageFilter()
    stats.Execute(image)
    print(f"--- 图像诊断: {name} ---")
    print(f"  尺寸: {image.GetSize()}, 像素类型: {image.GetPixelIDTypeAsString()}")
    print(f"  最小值: {stats.GetMinimum():.6f}, 最大值: {stats.GetMaximum():.6f}")
    print(f"  平均值: {stats.GetMean():.6f}, 标准差: {stats.GetSigma():.6f}")
    if stats.GetMaximum() <= 0.0: print(f"  [!!!] 警告: 图像 '{name}' 是空的！")
    return stats

print_stats(moving_image_affined_original, "w_pet_affine_only.nii.gz")

print("\n--- 仿射配准流程完成（仅仿射，无非线性变形）---")
print("此流程仅执行仿射配准，未包含B样条非线性配准及调制、雅可比等后续步骤。")