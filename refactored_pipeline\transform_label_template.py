import SimpleITK as sitk
import os

def transform_label_template_to_pet(template_path, pet_path, affine_path, output_inverse_path):
    """
    标签模板逆变换到PET空间，使用最近邻插值，输出整型，严格按照nibianhuan_temp.py实现，包含诊断输出。
    """
    # 读取图像
    template_img = sitk.ReadImage(template_path, sitk.sitkFloat32)
    pet_img = sitk.ReadImage(pet_path, sitk.sitkFloat32)
    print(f"模板图像像素类型: {template_img.GetPixelIDTypeAsString()}")
    print(f"模板图像数值范围: {sitk.GetArrayFromImage(template_img).min()} - {sitk.GetArrayFromImage(template_img).max()}")
    # 读取变换并取逆
    affine = sitk.ReadTransform(affine_path)
    affine_inv = affine.GetInverse()
    # 组合逆变换（为兼容性保留，实际只用仿射）
    composite_inv = sitk.CompositeTransform(3)
    composite_inv.AddTransform(affine_inv)
    # 逆变换重采样 - 对标签图像使用最近邻插值
    resampler = sitk.ResampleImageFilter()
    resampler.SetReferenceImage(pet_img)
    resampler.SetInterpolator(sitk.sitkNearestNeighbor)
    resampler.SetDefaultPixelValue(0)
    resampler.SetTransform(affine_inv)
    template_in_pet_space = resampler.Execute(template_img)
    # 将结果转换回整型以保持标签的准确性
    template_in_pet_space = sitk.Cast(template_in_pet_space, sitk.sitkInt32)
    # 保存结果
    sitk.WriteImage(template_in_pet_space, output_inverse_path)
    # print(f"模板已通过仿射逆变换映射到PET空间，结果保存为: {output_inverse_path}")
    # print(f"变换后图像数值范围: {sitk.GetArrayFromImage(template_in_pet_space).min()} - {sitk.GetArrayFromImage(template_in_pet_space).max()}")
    # print("✓ 使用最近邻插值，标签值保持完整")
    return output_inverse_path 