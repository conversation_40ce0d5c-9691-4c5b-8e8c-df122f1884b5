import os
import sys
from typing import List


def find_dicom_files(root_dir: str) -> List[str]:
    files: List[str] = []
    for entry in sorted(os.listdir(root_dir)):
        if entry.lower().endswith(".dcm"):
            files.append(os.path.join(root_dir, entry))
    return files


def ensure_backup(file_path: str) -> None:
    backup_path = file_path + ".bak"
    if not os.path.exists(backup_path):
        try:
            with open(file_path, "rb") as rf, open(backup_path, "wb") as wf:
                wf.write(rf.read())
        except Exception as exc:
            print(f"[WARN] 备份失败: {file_path} -> {backup_path}: {exc}")


# 用 pydicom 写入到输出目录（修改 Rescale Type）
def set_rescale_type_pydicom(directory: str, output_dir: str, value: str = "BQML") -> int:
    try:
        import pydicom  # type: ignore
        from pydicom import dcmread  # type: ignore
        from pydicom.tag import Tag  # type: ignore
        from pydicom.dataelem import DataElement  # type: ignore
        from pydicom.filewriter import dcmwrite  # type: ignore
    except Exception:
        return -1

    os.makedirs(output_dir, exist_ok=True)
    files = find_dicom_files(directory)
    count = 0
    for fp in files:
        try:
            ds = dcmread(fp, force=True)
            tag = Tag(0x0028, 0x1054)
            ds[tag] = DataElement(tag, "LO", value)
            dst = os.path.join(output_dir, os.path.basename(fp))
            dcmwrite(dst, ds, write_like_original=True)
            count += 1
        except Exception as exc:
            print(f"[ERROR] pydicom 写入失败: {fp}: {exc}")
    return count


# 将标准化像素写回新的 DICOM 文件（int32 像素，Slope=1/Intercept=0）
def normalize_pixels_to_dicom(directory: str, norm_dicom_out_dir: str, rescale_type_value: str = "BQML") -> int:
    try:
        import numpy as np  # type: ignore
        import pydicom  # type: ignore
        from pydicom import dcmread  # type: ignore
        from pydicom.tag import Tag  # type: ignore
        from pydicom.dataelem import DataElement  # type: ignore
        from pydicom.filewriter import dcmwrite  # type: ignore
    except Exception:
        return -1

    os.makedirs(norm_dicom_out_dir, exist_ok=True)
    files = find_dicom_files(directory)
    count = 0
    for fp in files:
        try:
            ds = dcmread(fp, force=True)
            if not hasattr(ds, 'pixel_array'):
                print(f"[WARN] 无像素数据: {fp}")
                continue
            pixels = ds.pixel_array.astype('float32')
            slope = float(getattr(ds, 'RescaleSlope', 1.0))
            intercept = float(getattr(ds, 'RescaleIntercept', 0.0))
            normalized = pixels * slope + intercept

            norm_int32 = normalized.round().astype('int32')

            ds.RescaleSlope = 1
            ds.RescaleIntercept = 0
            try:
                ds.SmallestImagePixelValue = int(norm_int32.min())
                ds.LargestImagePixelValue = int(norm_int32.max())
            except Exception:
                pass

            ds.BitsAllocated = 32
            ds.BitsStored = 32
            ds.HighBit = 31
            ds.PixelRepresentation = 1  # 1 = signed

            tag_rt = Tag(0x0028, 0x1054)
            ds[tag_rt] = DataElement(tag_rt, "LO", rescale_type_value)

            ds.PixelData = norm_int32.tobytes()

            dst = os.path.join(norm_dicom_out_dir, os.path.basename(fp))
            dcmwrite(dst, ds, write_like_original=False)
            count += 1
        except Exception as exc:
            print(f"[ERROR] 标准化写回 DICOM 失败: {fp}: {exc}")
    return count


def main() -> None:
    if len(sys.argv) < 4:
        print("用法: python dicom.py <输入DICOM目录> <DICOM输出目录(仅改Tag)> <标准化DICOM输出目录> [RescaleType值(默认BQML)]")
        sys.exit(1)

    directory = sys.argv[1]
    dicom_out_dir = sys.argv[2]
    norm_dicom_out_dir = sys.argv[3]
    value = sys.argv[4] if len(sys.argv) >= 5 else "BQML"

    if not os.path.isdir(directory):
        print(f"[ERROR] 目录不存在: {directory}")
        sys.exit(1)

    print(f"输入目录: {directory}")
    print(f"DICOM输出目录(仅改Tag): {dicom_out_dir}")
    print(f"标准化DICOM输出目录: {norm_dicom_out_dir}")
    print(f"设置标签 (0028,1054) Rescale Type = '{value}'（使用 pydicom）")

    updated = set_rescale_type_pydicom(directory, dicom_out_dir, value=value)
    if updated == -1:
        print("[ERROR] 未检测到 pydicom 或写入失败，无法写入标签。请安装 pydicom。")
        sys.exit(2)
    print(f"写出仅修改标签的 DICOM：{updated} 个")

    normalized_dcm = normalize_pixels_to_dicom(directory, norm_dicom_out_dir, rescale_type_value=value)
    if normalized_dcm == -1:
        print("[ERROR] 标准化失败：需要 numpy 与 pydicom。请安装 numpy。")
        sys.exit(3)

    print(f"标准化 DICOM 写出完成：{normalized_dcm} 个")


if __name__ == "__main__":
    main()
