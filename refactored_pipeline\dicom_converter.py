import SimpleITK as sitk
import os
import glob
import numpy as np
from pathlib import Path

def convert_dicom_to_nifti(dicom_folder_path, output_path=None, series_description=None):
    """
    将DICOM序列转换为NIFTI格式
    
    Args:
        dicom_folder_path: DICOM文件夹路径
        output_path: 输出NIFTI文件路径，如果为None则自动生成
        series_description: 可选的序列描述过滤条件
    
    Returns:
        output_path: 输出的NIFTI文件路径
    """
    if not os.path.exists(dicom_folder_path):
        raise FileNotFoundError(f"DICOM文件夹不存在: {dicom_folder_path}")
    
    # 查找DICOM文件
    dicom_files = []
    for ext in ['*.dcm', '*.DCM', '*.dicom', '*.DICOM']:
        dicom_files.extend(glob.glob(os.path.join(dicom_folder_path, ext)))
    
    if not dicom_files:
        raise FileNotFoundError(f"在文件夹 {dicom_folder_path} 中未找到DICOM文件")
    
    print(f"找到 {len(dicom_files)} 个DICOM文件")
    
    # 读取DICOM序列
    reader = sitk.ImageSeriesReader()
    
    # 获取DICOM系列ID
    series_ids = reader.GetGDCMSeriesIDs(dicom_folder_path)
    
    if not series_ids:
        raise ValueError(f"在文件夹 {dicom_folder_path} 中未找到有效的DICOM系列")
    
    print(f"找到 {len(series_ids)} 个DICOM系列")
    
    # 如果指定了序列描述，尝试匹配
    selected_series_id = series_ids[0]  # 默认选择第一个系列
    if series_description:
        for series_id in series_ids:
            series_file_names = reader.GetGDCMSeriesFileNames(dicom_folder_path, series_id)
            if series_file_names:
                # 读取第一个文件获取序列描述
                temp_reader = sitk.ImageFileReader()
                temp_reader.SetFileName(series_file_names[0])
                temp_reader.LoadPrivateTagsOn()
                temp_reader.ReadImageInformation()
                
                try:
                    desc = temp_reader.GetMetaData("0008|103e")  # Series Description
                    if series_description.lower() in desc.lower():
                        selected_series_id = series_id
                        print(f"匹配到序列描述: {desc}")
                        break
                except:
                    continue
    
    # 获取选定系列的文件名
    series_file_names = reader.GetGDCMSeriesFileNames(dicom_folder_path, selected_series_id)
    
    if not series_file_names:
        raise ValueError(f"无法获取系列 {selected_series_id} 的文件名")
    
    print(f"使用系列 {selected_series_id}，包含 {len(series_file_names)} 个文件")
    
    # 设置文件名并读取图像
    reader.SetFileNames(series_file_names)
    image = reader.Execute()
    
    # 获取图像信息
    size = image.GetSize()
    spacing = image.GetSpacing()
    origin = image.GetOrigin()
    direction = image.GetDirection()
    
    print(f"图像尺寸: {size}")
    print(f"像素间距: {spacing}")
    print(f"原点: {origin}")
    
    # 生成输出路径
    if output_path is None:
        folder_name = os.path.basename(dicom_folder_path)
        output_path = os.path.join(dicom_folder_path, f"{folder_name}.nii.gz")
    
    # 确保输出目录存在
    output_dir = os.path.dirname(output_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 保存为NIFTI格式
    sitk.WriteImage(image, output_path)
    print(f"DICOM序列已转换为: {output_path}")
    
    return output_path

def is_dicom_folder(folder_path):
    """
    检查文件夹是否包含DICOM文件
    
    Args:
        folder_path: 文件夹路径
    
    Returns:
        bool: 是否包含DICOM文件
    """
    if not os.path.exists(folder_path):
        return False
    
    # 检查常见的DICOM文件扩展名
    dicom_extensions = ['*.dcm', '*.DCM', '*.dicom', '*.DICOM']
    for ext in dicom_extensions:
        if glob.glob(os.path.join(folder_path, ext)):
            return True
    
    return False

def get_dicom_info(dicom_folder_path):
    """
    获取DICOM序列的基本信息
    
    Args:
        dicom_folder_path: DICOM文件夹路径
    
    Returns:
        dict: DICOM信息字典
    """
    if not is_dicom_folder(dicom_folder_path):
        return None
    
    try:
        reader = sitk.ImageSeriesReader()
        series_ids = reader.GetGDCMSeriesIDs(dicom_folder_path)
        
        if not series_ids:
            return None
        
        # 获取第一个系列的信息
        series_file_names = reader.GetGDCMSeriesFileNames(dicom_folder_path, series_ids[0])
        if not series_file_names:
            return None
        
        # 读取第一个文件获取元数据
        temp_reader = sitk.ImageFileReader()
        temp_reader.SetFileName(series_file_names[0])
        temp_reader.LoadPrivateTagsOn()
        temp_reader.ReadImageInformation()
        
        # 读取图像获取基本信息
        reader.SetFileNames(series_file_names)
        image = reader.Execute()
        
        info = {
            'series_count': len(series_ids),
            'file_count': len(series_file_names),
            'size': image.GetSize(),
            'spacing': image.GetSpacing(),
            'origin': image.GetOrigin(),
            'pixel_type': image.GetPixelIDTypeAsString(),
            'series_ids': series_ids
        }
        
        # 尝试获取更多元数据
        try:
            info['patient_name'] = temp_reader.GetMetaData("0010|0010")
        except:
            info['patient_name'] = "Unknown"
        
        try:
            info['study_description'] = temp_reader.GetMetaData("0008|1030")
        except:
            info['study_description'] = "Unknown"
        
        try:
            info['series_description'] = temp_reader.GetMetaData("0008|103e")
        except:
            info['series_description'] = "Unknown"
        
        return info
        
    except Exception as e:
        print(f"获取DICOM信息时出错: {e}")
        return None

def convert_if_dicom(input_path, output_folder=None, series_description=None):
    """
    如果输入路径是DICOM文件夹，则转换为NIFTI；否则返回原路径
    
    Args:
        input_path: 输入路径（可能是DICOM文件夹或NIFTI文件）
        output_folder: 输出文件夹路径
        series_description: 可选的序列描述过滤条件
    
    Returns:
        str: 转换后的文件路径或原路径
    """
    if os.path.isfile(input_path):
        # 已经是文件，检查是否为NIFTI格式
        if input_path.endswith(('.nii', '.nii.gz', '.nii.gz')):
            return input_path
        else:
            raise ValueError(f"不支持的文件格式: {input_path}")
    
    elif os.path.isdir(input_path):
        # 检查是否为DICOM文件夹
        if is_dicom_folder(input_path):
            print(f"检测到DICOM文件夹: {input_path}")
            
            # 获取DICOM信息
            dicom_info = get_dicom_info(input_path)
            if dicom_info:
                print(f"DICOM信息: {dicom_info['series_count']} 个系列, {dicom_info['file_count']} 个文件")
                print(f"图像尺寸: {dicom_info['size']}")
                if dicom_info['series_description'] != "Unknown":
                    print(f"序列描述: {dicom_info['series_description']}")
            
            # 转换DICOM到NIFTI
            if output_folder is None:
                output_folder = input_path
            
            folder_name = os.path.basename(input_path)
            output_path = os.path.join(output_folder, f"{folder_name}.nii.gz")
            
            return convert_dicom_to_nifti(input_path, output_path, series_description)
        else:
            raise ValueError(f"文件夹 {input_path} 不包含DICOM文件")
    else:
        raise FileNotFoundError(f"路径不存在: {input_path}")

def batch_convert_dicom_folders(input_folders, output_folder, series_description=None):
    """
    批量转换多个DICOM文件夹
    
    Args:
        input_folders: DICOM文件夹路径列表
        output_folder: 输出文件夹路径
        series_description: 可选的序列描述过滤条件
    
    Returns:
        list: 转换后的文件路径列表
    """
    converted_files = []
    
    for folder_path in input_folders:
        try:
            print(f"\n处理文件夹: {folder_path}")
            converted_path = convert_if_dicom(folder_path, output_folder, series_description)
            converted_files.append(converted_path)
            print(f"转换完成: {converted_path}")
        except Exception as e:
            print(f"转换失败 {folder_path}: {e}")
            continue
    
    return converted_files 