#!/usr/bin/env python3
"""
测试GPU加速的豪斯多夫距离计算
"""

import SimpleITK as sitk
import numpy as np
import time
import os

# 导入我们的函数
from contact import calculate_segmentation_metrics_manual, calculate_segmentation_metrics_per_label

def test_gpu_hausdorff():
    """测试GPU加速的豪斯多夫距离计算性能"""
    
    # 文件路径
    file1_path = 'output/pmod_result/N30R83_1MM_2/parcel_s2_Brain_segments__Hammers_N30R83_1mm_atlas_region_labels____NORMALIZED_to_TOF_AC_SC_GF_MF_1_0____Atlas_in_Functional_space_Hammers_N30R83_1MM.nii'
    file2_path = 'output/my_result/Hammers-N30R83-1MM/Hammers-N30R83-1MM_withWM.nii.gz'
    label_range = (1, 130)
    
    print("=== GPU加速豪斯多夫距离测试 ===\n")
    
    # 检查文件是否存在
    if not os.path.exists(file1_path) or not os.path.exists(file2_path):
        print("❌ 测试文件不存在")
        return
    
    print("1. 测试整体前景豪斯多夫距离计算...")
    
    # GPU版本
    print("\n   GPU版本:")
    start_time = time.time()
    try:
        results_gpu = calculate_segmentation_metrics_manual(
            file1_path, file2_path, 
            label_range=label_range, 
            use_gpu=True
        )
        gpu_time = time.time() - start_time
        if results_gpu:
            print(f"   ✅ GPU豪斯多夫距离: {results_gpu['Hausdorff']:.4f}")
            print(f"   ⏱️  GPU计算时间: {gpu_time:.2f}秒")
        else:
            print("   ❌ GPU计算失败")
    except Exception as e:
        print(f"   ❌ GPU计算出错: {e}")
        gpu_time = float('inf')
    
    # CPU版本
    print("\n   CPU版本:")
    start_time = time.time()
    try:
        results_cpu = calculate_segmentation_metrics_manual(
            file1_path, file2_path, 
            label_range=label_range, 
            use_gpu=False
        )
        cpu_time = time.time() - start_time
        if results_cpu:
            print(f"   ✅ CPU豪斯多夫距离: {results_cpu['Hausdorff']:.4f}")
            print(f"   ⏱️  CPU计算时间: {cpu_time:.2f}秒")
        else:
            print("   ❌ CPU计算失败")
    except Exception as e:
        print(f"   ❌ CPU计算出错: {e}")
        cpu_time = float('inf')
    
    # 性能比较
    if gpu_time != float('inf') and cpu_time != float('inf'):
        speedup = cpu_time / gpu_time
        print(f"\n   📊 性能提升: {speedup:.2f}x")
    
    print("\n" + "="*50)
    print("2. 测试逐标签豪斯多夫距离计算（前5个标签）...")
    
    # 限制测试标签数量以节省时间
    test_labels = list(range(1, 6))  # 测试前5个标签
    
    # GPU版本
    print("\n   GPU版本（逐标签）:")
    start_time = time.time()
    try:
        results_gpu_per_label = calculate_segmentation_metrics_per_label(
            file1_path, file2_path,
            label_range=label_range,
            labels=test_labels,
            use_gpu=True,
            compute_hausdorff_per_label=True
        )
        gpu_per_label_time = time.time() - start_time
        
        if results_gpu_per_label:
            print(f"   ✅ 成功计算{len(results_gpu_per_label)}个标签")
            for label in sorted(results_gpu_per_label.keys())[:3]:  # 显示前3个
                hausdorff = results_gpu_per_label[label]['Hausdorff']
                print(f"     标签{label}: Hausdorff={hausdorff:.4f}")
            print(f"   ⏱️  GPU逐标签计算时间: {gpu_per_label_time:.2f}秒")
        else:
            print("   ❌ GPU逐标签计算失败")
    except Exception as e:
        print(f"   ❌ GPU逐标签计算出错: {e}")
        gpu_per_label_time = float('inf')
    
    # CPU版本
    print("\n   CPU版本（逐标签）:")
    start_time = time.time()
    try:
        results_cpu_per_label = calculate_segmentation_metrics_per_label(
            file1_path, file2_path,
            label_range=label_range,
            labels=test_labels,
            use_gpu=False,
            compute_hausdorff_per_label=True
        )
        cpu_per_label_time = time.time() - start_time
        
        if results_cpu_per_label:
            print(f"   ✅ 成功计算{len(results_cpu_per_label)}个标签")
            for label in sorted(results_cpu_per_label.keys())[:3]:  # 显示前3个
                hausdorff = results_cpu_per_label[label]['Hausdorff']
                print(f"     标签{label}: Hausdorff={hausdorff:.4f}")
            print(f"   ⏱️  CPU逐标签计算时间: {cpu_per_label_time:.2f}秒")
        else:
            print("   ❌ CPU逐标签计算失败")
    except Exception as e:
        print(f"   ❌ CPU逐标签计算出错: {e}")
        cpu_per_label_time = float('inf')
    
    # 逐标签性能比较
    if gpu_per_label_time != float('inf') and cpu_per_label_time != float('inf'):
        speedup_per_label = cpu_per_label_time / gpu_per_label_time
        print(f"\n   📊 逐标签性能提升: {speedup_per_label:.2f}x")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_gpu_hausdorff()
