import SimpleITK as sitk
import numpy as np
import os
import csv # 引入Python内置的CSV模块

# 新增函数: 读取 AAL-Merged.txt 构建标签映射

def load_aal_mapping(aal_txt_path):
    """
    读取 AAL-Merged.txt 文件, 返回 {label_id: region_name} 的映射字典。
    若文件不存在, 返回空映射并在控制台提示。
    """
    mapping = {}
    if not os.path.exists(aal_txt_path):
        print(f"警告: 未找到 AAL 标签文件 {aal_txt_path}, 将使用数字标签。")
        return mapping

    with open(aal_txt_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            parts = line.split('\t')
            if len(parts) < 3:
                continue
            try:
                label_id = int(parts[2])
            except ValueError:
                continue
            region_name = parts[1]
            mapping[label_id] = region_name
    return mapping

def perform_quantitative_analysis_and_save_to_csv(pet_path, aal_path, output_csv_path):
    """
    对PET图像进行基于AAL模板的定量化区域分析，并将结果保存为CSV文件。

    参数:
    pet_path (str): 与AAL对齐后的PET nifti图像的路径。
    aal_path (str): AAL模板 nifti文件的路径。
    output_csv_path (str): 输出的CSV文件的保存路径。
    """
    print("--- 开始进行定量分析 ---")
    
    try:
        # --- 步骤 1: 加载图像 ---
        pet_image = sitk.ReadImage(pet_path, sitk.sitkFloat32)
        aal_image = sitk.ReadImage(aal_path, sitk.sitkUInt32)
        print("1. 成功加载PET图像和AAL模板。")

        # --- 步骤 2: 验证图像对齐 ---
        if pet_image.GetSize() != aal_image.GetSize():
            print("\n警告：PET图像和AAL模板的尺寸不匹配。")
            print("结果可能不准确。强烈建议先将PET图像重采样到AAL模板空间。\n")

        # --- 步骤 3: 使用LabelStatisticsImageFilter进行核心分析 ---
        label_stats_filter = sitk.LabelStatisticsImageFilter()
        label_stats_filter.Execute(pet_image, aal_image)
        print("2. 已成功计算每个AAL脑区的PET统计数据。")

        # === 加载 AAL 标签映射 ===
        aal_txt_path = os.path.join(os.path.dirname(aal_path), "AAL-Merged.txt")
        label_mapping = load_aal_mapping(aal_txt_path)
        # 新增：自定义标签映射（如80为白质）
        custom_label_mapping = {80: "White Matter"}
        # 合并自定义标签映射（优先AAL-Merged.txt）
        label_mapping = {**custom_label_mapping, **label_mapping}

        # --- 步骤 4: 将结果写入CSV文件 ---
        print(f"3. 正在将分析结果写入CSV文件: {output_csv_path}")
        
        # 使用'with'语句确保文件能被正确关闭
        with open(output_csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            # 创建一个CSV写入对象
            csv_writer = csv.writer(csvfile)
            
            # 写入表头 (Header)
            header = ['AAL_Region_Name', 'Mean_PET_Value', 'Standard_Deviation', 'Voxel_Count', 'Volume_ccm']
            csv_writer.writerow(header)
            
            # 遍历所有在AAL模板中存在的标签
            for label_value in sorted(label_stats_filter.GetLabels()):
                if label_value == 0:  # 忽略背景标签
                    continue
                
                # 获取统计数据
                mean_val = label_stats_filter.GetMean(label_value) / 1000  # 将平均值除以1000
                std_dev_val = label_stats_filter.GetSigma(label_value)
                count_val = label_stats_filter.GetCount(label_value)

                # === 计算体积（立方厘米） ===
                # 使用PET图像的体素间距，单位mm
                spacing = pet_image.GetSpacing()  # (x, y, z) in mm
                voxel_volume_mm3 = spacing[0] * spacing[1] * spacing[2]
                volume_ccm = (count_val * voxel_volume_mm3) / 1000.0  # 1 cm^3 = 1000 mm^3
                
                # 将标签数字映射为名称, 若无映射则退回数字
                region_name = label_mapping.get(label_value, str(label_value))
                # 将数据写入一行
                data_row = [region_name, mean_val, std_dev_val, count_val, volume_ccm]
                csv_writer.writerow(data_row)
        
        print("   写入完成！")

        # --- 步骤 5: 额外检查 (结果打印在屏幕上) ---
        print("\n--- 额外分析：检查被AAL模板忽略的PET信号 ---")
        pet_np = sitk.GetArrayViewFromImage(pet_image)
        aal_np = sitk.GetArrayViewFromImage(aal_image)
        significant_pet_mask = pet_np > (pet_np.mean() * 1.5) # 定义一个动态的“显著”阈值
        outside_aal_mask = aal_np == 0
        missed_voxels_mask = significant_pet_mask & outside_aal_mask
        num_missed_voxels = np.sum(missed_voxels_mask)
        
        if num_missed_voxels > 0:
            missed_pet_values = pet_np[missed_voxels_mask]
            print(f"发现在AAL模板之外，存在 {num_missed_voxels} 个具有显著信号的体素。")
            print(f"这些被忽略的体素的PET信号平均值为: {np.mean(missed_pet_values) / 1000:.2f}")  # 将平均值除以1000
        else:
            print("未在AAL模板之外发现显著的PET信号。模板覆盖良好。")
            
        print("\n--- 所有处理已完成！ ---")
        print(f"定量分析结果已成功保存到 '{output_csv_path}'。")

    except Exception as e:
        print(f"\n处理过程中发生严重错误: {e}")

# --- 主程序入口 ---
if __name__ == '__main__':
    
    # ==========================================================
    # --- 请在这里修改您的文件路径 ---
    # ==========================================================
    
    # 1. 您的PET图像路径 (必须与AAL模板在同一空间)
    pet_file_aligned = "li_yunqiao_PET/NIFTI_Output_Folder/Series.nii.gz"
    
    # 2. 您的AAL模板路径
    aal_file = "output/test_PET_spm_style/AAL_intersected_pureGM_p05.nii.gz"
    
    # 3. 您希望保存的CSV表格的文件名
    output_csv_file = "output/test_PET_spm_style/pet_aal_quantitative_results.csv"
    
    # ==========================================================
    
    # 检查输入文件是否存在
    if not os.path.exists(pet_file_aligned) or not os.path.exists(aal_file):
        print("!!!错误：找不到输入文件!!!")
        print(f"请确保以下文件路径正确:\n- 对齐后的PET文件: {pet_file_aligned}\n- AAL模板: {aal_file}")
    else:
        perform_quantitative_analysis_and_save_to_csv(pet_file_aligned, 
                                                      aal_file, 
                                                      output_csv_file)





    #         # --- 请在这里修改您的文件路径 ---
    # # 您的PET图像路径 (已经和AAL模板配准过)
    # pet_file = "output/test_PET/registered_image_final_bspline.nii.gz"
    # # 您的AAL模板路径
    # aal_file = "PMOD-VOI/AAL-Merged/AAL-Merged.nii.gz"
    # # 您希望保存融合图像的路径
    # output_file = "output/test_PET/fused_pet_aal.nii.gz"