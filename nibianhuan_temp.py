import SimpleITK as sitk
import os

# 路径设置
template_path = "PMOD-VOI/AAL-VOIs-NEURO.nii.gz"  # 模板
pet_path = "li_yunqiao_PET/NIFTI_Output_Folder/Series.nii.gz"  # 源PET
affine_path = "output/my_result/AAL-VOIs/final_affine_transform.tfm"
output_inverse_path = "output/my_result/AAL-VOIs/template_to_pet_inverse_affineonly.nii.gz"

# 读取图像
template_img = sitk.ReadImage(template_path, sitk.sitkFloat32)
pet_img = sitk.ReadImage(pet_path, sitk.sitkFloat32)

# 对于标签图像，需要特殊处理以保持整型标签
print(f"模板图像像素类型: {template_img.GetPixelIDTypeAsString()}")
print(f"模板图像数值范围: {sitk.GetArrayFromImage(template_img).min()} - {sitk.GetArrayFromImage(template_img).max()}")

# 读取变换并取逆
affine = sitk.ReadTransform(affine_path)
affine_inv = affine.GetInverse()

# 组合逆变换（先B样条逆，再仿射逆）
composite_inv = sitk.CompositeTransform(3)
composite_inv.AddTransform(affine_inv)

# 逆变换重采样 - 对标签图像使用最近邻插值
resampler = sitk.ResampleImageFilter()
resampler.SetReferenceImage(pet_img)
resampler.SetInterpolator(sitk.sitkNearestNeighbor)  # 改为最近邻插值，保持标签完整性
resampler.SetDefaultPixelValue(0)
resampler.SetTransform(affine_inv)
template_in_pet_space = resampler.Execute(template_img)

# 将结果转换回整型以保持标签的准确性
template_in_pet_space = sitk.Cast(template_in_pet_space, sitk.sitkInt32)

# 保存结果
sitk.WriteImage(template_in_pet_space, output_inverse_path)
print(f"模板已通过仿射逆变换映射到PET空间，结果保存为: {output_inverse_path}")
print(f"变换后图像数值范围: {sitk.GetArrayFromImage(template_in_pet_space).min()} - {sitk.GetArrayFromImage(template_in_pet_space).max()}")
print("✓ 使用最近邻插值，标签值保持完整")
