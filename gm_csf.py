import nibabel as nib
import numpy as np
import os

# --- 1. 设置文件路径 ---
# 将此路径替换为您电脑上 tpm.nii.gz 文件的实际路径
tpm_filepath = 'PMOD-VOI/Hammers-N30R83-1MM/normalization/tpm/tpm.nii.gz'

# 定义输出文件名
output_gm_path = 'gm_prob_map.nii.gz'
output_csf_path = 'csf_prob_map.nii.gz'

# --- 检查输入文件是否存在 ---
if not os.path.exists(tpm_filepath):
    print(f"错误：输入文件不存在，请检查路径: {tpm_filepath}")
else:
    try:
        # --- 2. 加载4D TPM文件 ---
        print(f"正在加载4D组织概率图谱: {tpm_filepath}")
        tpm_img = nib.load(tpm_filepath)

        # 获取图像数据（作为numpy数组）和空间仿射矩阵
        tpm_data = tpm_img.get_fdata()
        affine = tpm_img.affine

        # --- 3. 验证图像维度 ---
        if tpm_data.ndim != 4:
            print(f"错误: {tpm_filepath} 不是一个4D文件，无法提取多个图谱。")
        else:
            num_volumes = tpm_data.shape[3]
            print(f"文件加载成功，包含 {num_volumes} 个图谱。")

            # --- 4. 提取灰质 (Gray Matter, GM) 图谱 ---
            # 根据SPM的标准，GM是第1个图谱，在Python中的索引是0
            if num_volumes >= 1:
                print("正在提取灰质 (GM) 图谱 (第1卷)...")
                gm_data = tpm_data[:, :, :, 0]
                # 创建新的3D NIfTI图像对象
                gm_img = nib.Nifti1Image(gm_data, affine)
                # 保存文件
                nib.save(gm_img, output_gm_path)
                print(f"灰质图谱已成功保存至: {output_gm_path}")
            else:
                print("无法提取灰质图谱，文件卷数不足。")

            # --- 5. 提取脑脊液 (Cerebrospinal Fluid, CSF) 图谱 ---
            # 根据SPM的标准，CSF是第3个图谱，在Python中的索引是2
            if num_volumes >= 3:
                print("正在提取脑脊液 (CSF) 图谱 (第3卷)...")
                csf_data = tpm_data[:, :, :, 2]
                # 创建新的3D NIfTI图像对象
                csf_img = nib.Nifti1Image(csf_data, affine)
                # 保存文件
                nib.save(csf_img, output_csf_path)
                print(f"脑脊液图谱已成功保存至: {output_csf_path}")
            else:
                print("无法提取脑脊液图谱，文件卷数不足。")

            # --- 6. 提取白质 (White Matter, WM) 图谱 ---
            # 根据SPM的标准，WM是第2个图谱，在Python中的索引是1
            output_wm_path = 'wm_prob_map.nii.gz'
            if num_volumes >= 2:
                print("正在提取白质 (WM) 图谱 (第2卷)...")
                wm_data = tpm_data[:, :, :, 1]
                # 创建新的3D NIfTI图像对象
                wm_img = nib.Nifti1Image(wm_data, affine)
                # 保存文件
                nib.save(wm_img, output_wm_path)
                print(f"白质图谱已成功保存至: {output_wm_path}")
            else:
                print("无法提取白质图谱，文件卷数不足。")

    except Exception as e:
        print(f"处理过程中发生错误: {e}")