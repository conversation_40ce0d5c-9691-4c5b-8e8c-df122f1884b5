import SimpleITK as sitk
import os
import sys

# --- 步骤 1: 设置文件路径 ---
moving_image_path = "li_yunqiao_PET/NIFTI_Output_Folder/Series.nii.gz"
fixed_image_path = "normalization/PET.nii.gz"
output_folder_path = "output/test_PET_spm_style"

# --- 参数设置 ---
# 极保守的B样条设置，网格非常粗糙以防止过度配准
BSPLINE_GRID_SIZE = 9 # 最小网格密度，类似SPM的最保守设置

# --- 脚本主体 ---
if not all(os.path.exists(p) for p in [moving_image_path, fixed_image_path]):
    print("错误: 找不到输入的数据文件或模板文件。请仔细检查路径。")
    sys.exit(1)
if not os.path.isdir(output_folder_path):
    print(f"输出文件夹不存在，正在创建: {output_folder_path}")
    os.makedirs(output_folder_path)

print("--- 开始标准三步法图像配准流程 (仿照SPM风格) ---")
fixed_image = sitk.ReadImage(fixed_image_path, sitk.sitkFloat32)
moving_image = sitk.ReadImage(moving_image_path, sitk.sitkFloat32)

# PET定量保护配准策略：创建归一化副本用于配准，保持原始图像不变
print("正在进行PET定量保护的预处理...")

# 保存原始图像（保持定量数值）
original_fixed_image = fixed_image
original_moving_image = moving_image

def create_registration_copy(image, image_name):
    """创建用于配准的图像副本，仅用于配准算法，不影响最终定量结果"""
    stats = sitk.StatisticsImageFilter()
    stats.Execute(image)
    mean_val = stats.GetMean()
    max_val = stats.GetMaximum()
    
    print(f"  {image_name} 原始强度 - 均值: {mean_val:.2f}, 最大值: {max_val:.2f}")
    
    # 创建归一化副本仅用于配准优化
    if mean_val > 0:
        # 轻度归一化，避免过度改变强度分布
        normalized = sitk.Divide(image, mean_val / 50.0)
        stats.Execute(normalized)
        print(f"  {image_name} 配准副本 - 均值: {stats.GetMean():.2f}, 最大值: {stats.GetMaximum():.2f}")
        return normalized
    return image

# 创建用于配准的归一化副本
fixed_image = create_registration_copy(original_fixed_image, "固定图像")
moving_image = create_registration_copy(original_moving_image, "移动图像")

print("✓ 配准副本已创建，原始定量数值得到保护")

def registration_progress_update(registration_method, stage_name):
    current_iter = registration_method.GetOptimizerIteration()
    metric_value = registration_method.GetMetricValue()
    stop_condition = registration_method.GetOptimizerStopConditionDescription()
    print(f"阶段: {stage_name} | 迭代: {current_iter:3} | 度量值: {metric_value:10.5f} | 停止条件: {stop_condition}      ", end='\r')

# --- 第一阶段: 仿射配准 ---
print("\n--- 第一阶段: 仿射配准 (处理全局对齐) ---")
affine_registration = sitk.ImageRegistrationMethod()
affine_registration.SetMetricAsMattesMutualInformation(numberOfHistogramBins=50)
affine_registration.SetMetricSamplingStrategy(affine_registration.RANDOM)
# SPM使用更多样本以提高配准稳定性
affine_registration.SetMetricSamplingPercentage(0.05)
affine_registration.SetInterpolator(sitk.sitkLinear)
# 更稳定的优化器设置，类似SPM的多步优化策略
affine_registration.SetOptimizerAsRegularStepGradientDescent(learningRate=2.0, minStep=1e-6, numberOfIterations=300, relaxationFactor=0.8)
affine_registration.SetOptimizerScalesFromPhysicalShift()
# SPM风格的多分辨率策略，增加一个更粗糙的层级
affine_registration.SetShrinkFactorsPerLevel([8, 4, 2, 1])
affine_registration.SetSmoothingSigmasPerLevel([4, 2, 1, 0])
affine_registration.SmoothingSigmasAreSpecifiedInPhysicalUnitsOn()
# 使用MOMENTS初始化，比GEOMETRY更稳定，类似SPM的质心对齐
initial_transform = sitk.CenteredTransformInitializer(fixed_image, moving_image, sitk.AffineTransform(3), sitk.CenteredTransformInitializerFilter.MOMENTS)
affine_registration.SetInitialTransform(initial_transform)
affine_registration.AddCommand(sitk.sitkIterationEvent, lambda: registration_progress_update(affine_registration, "仿射"))
final_affine_transform = affine_registration.Execute(fixed_image, moving_image)
# 保存仿射变换
sitk.WriteTransform(final_affine_transform, os.path.join(output_folder_path, "final_affine_transform.tfm"))
print(f"仿射变换已保存至: {os.path.join(output_folder_path, 'final_affine_transform.tfm')}")
print(f"\n仿射配准完成。")
print(f"最终度量值: {affine_registration.GetMetricValue():.6f}")
print(f"优化器停止原因: {affine_registration.GetOptimizerStopConditionDescription()}")
affine_registration.RemoveAllCommands()

# --- 第二阶段: "固化"仿射结果（保持定量数值）---
print("\n--- 第二阶段: 应用仿射变换，生成临时对齐图像 ---")
# 对原始图像应用仿射变换以保持定量数值
moving_image_affined_original = sitk.Resample(original_moving_image, original_fixed_image, final_affine_transform, sitk.sitkLinear, 0.0, original_moving_image.GetPixelID())
sitk.WriteImage(moving_image_affined_original, os.path.join(output_folder_path, "w_pet_affine_only.nii.gz"))

# 对归一化图像应用仿射变换用于后续B样条配准
moving_image_affined = sitk.Resample(moving_image, fixed_image, final_affine_transform, sitk.sitkLinear, 0.0, moving_image.GetPixelID())
print("仿射对齐的临时图像已生成（原始定量和配准副本）。")

# --- 第三阶段: B-样条可变形配准 ---
print("\n--- 第三阶段: B-样条可变形配准 (处理局部形变) ---")
bspline_registration = sitk.ImageRegistrationMethod()
bspline_registration.SetMetricAsMattesMutualInformation(numberOfHistogramBins=50)
bspline_registration.SetMetricSamplingStrategy(bspline_registration.RANDOM)
# B样条阶段使用适中的样本以避免过度拟合
bspline_registration.SetMetricSamplingPercentage(0.05)
bspline_registration.SetInterpolator(sitk.sitkLinear)
# 极保守的优化器设置，严格防止过度配准
bspline_registration.SetOptimizerAsLBFGSB(
    gradientConvergenceTolerance=1e-4,  # 放宽收敛条件
    numberOfIterations=5,  # 大幅减少迭代次数
    maximumNumberOfCorrections=3, 
    maximumNumberOfFunctionEvaluations=50  # 严格限制函数评估次数
)
# 更保守的多分辨率策略，只使用两个层级
bspline_registration.SetShrinkFactorsPerLevel([1])
bspline_registration.SetSmoothingSigmasPerLevel([2])
bspline_registration.SmoothingSigmasAreSpecifiedInPhysicalUnitsOn()
bspline_transform = sitk.BSplineTransformInitializer(fixed_image, [BSPLINE_GRID_SIZE] * fixed_image.GetDimension())
bspline_registration.SetInitialTransform(bspline_transform, inPlace=True)
bspline_registration.AddCommand(sitk.sitkIterationEvent, lambda: registration_progress_update(bspline_registration, "B样条"))
final_bspline_transform = bspline_registration.Execute(fixed_image, moving_image_affined)
# 保存B样条变换
sitk.WriteTransform(final_bspline_transform, os.path.join(output_folder_path, "final_bspline_transform.tfm"))
print(f"B样条变换已保存至: {os.path.join(output_folder_path, 'final_bspline_transform.tfm')}")
print(f"\nB-样条配准完成。")

# 验证B样条变形的合理性
print("\n验证B样条变形场...")
def validate_bspline_transform(bspline_transform):
    """验证B样条变换的参数是否合理"""
    import numpy as np
    params = bspline_transform.GetParameters()
    params_array = np.array(params)
    max_deformation = max(abs(params_array.max()), abs(params_array.min()))
    mean_deformation = np.abs(params_array).mean()
    print(f"  B样条参数范围: [{params_array.min():.6f}, {params_array.max():.6f}]")
    print(f"  最大变形: {max_deformation:.6f}mm, 平均变形: {mean_deformation:.6f}mm")
    if max_deformation > 20:  # 超过20mm的变形可能不合理
        print("  [警告] 变形可能过大，建议检查配准参数！")
    return max_deformation < 20

is_bspline_valid = validate_bspline_transform(final_bspline_transform)
bspline_registration.RemoveAllCommands()

# --- 第四阶段: 智能变换选择和结果生成 ---
print("\n--- 第四阶段: 智能变换选择并生成所有输出文件 ---")

# 【新增】无论B样条是否被采用，都先保存B样条配准结果
print("正在保存B样条配准的图像（无论是否被最终采用）...")
bspline_resampler = sitk.ResampleImageFilter()
bspline_resampler.SetReferenceImage(original_fixed_image)
bspline_resampler.SetInterpolator(sitk.sitkLinear)
bspline_resampler.SetDefaultPixelValue(0)
bspline_resampler.SetTransform(sitk.CompositeTransform([final_affine_transform, final_bspline_transform]))
warped_bspline_image = bspline_resampler.Execute(original_moving_image)
output_bspline_path = os.path.join(output_folder_path, "w_pet_bspline.nii.gz")
sitk.WriteImage(warped_bspline_image, output_bspline_path)
print(f"B样条配准图像已保存至: {output_bspline_path}")



# 如果B样条变形过大，回退到仅使用仿射配准
if not is_bspline_valid:
    print("⚠ 检测到B样条过度配准，回退到仅使用仿射配准...")
    final_transform = final_affine_transform
    use_bspline = False
else:
    print("✓ B样条配准正常，使用完整的复合变换...")
    final_transform = sitk.CompositeTransform([final_affine_transform, final_bspline_transform])
    use_bspline = True
# 保存最终复合变换
sitk.WriteTransform(final_transform, os.path.join(output_folder_path, "final_composite_transform.tfm"))
print(f"最终复合变换已保存至: {os.path.join(output_folder_path, 'final_composite_transform.tfm')}")

# --- 诊断和生成所有文件 ---
def print_stats(image, name):
    stats = sitk.StatisticsImageFilter()
    stats.Execute(image)
    print(f"--- 图像诊断: {name} ---")
    print(f"  尺寸: {image.GetSize()}, 像素类型: {image.GetPixelIDTypeAsString()}")
    print(f"  最小值: {stats.GetMinimum():.6f}, 最大值: {stats.GetMaximum():.6f}")
    print(f"  平均值: {stats.GetMean():.6f}, 标准差: {stats.GetSigma():.6f}")
    if stats.GetMaximum() <= 0.0: print(f"  [!!!] 警告: 图像 '{name}' 是空的！")
    return stats

# 【关键】应用变换到原始图像以保持定量数值
print("正在应用变换到原始PET图像以保持定量数值...")
resampler = sitk.ResampleImageFilter()
resampler.SetReferenceImage(original_fixed_image)  # 使用原始固定图像作为参考
resampler.SetInterpolator(sitk.sitkLinear)
resampler.SetDefaultPixelValue(0)
resampler.SetTransform(final_transform)
warped_image = resampler.Execute(original_moving_image)  # 对原始移动图像应用变换

# 根据使用的配准方法调整文件名
if use_bspline:
    output_warped_path = os.path.join(output_folder_path, "w_pet.nii.gz")
else:
    output_warped_path = os.path.join(output_folder_path, "w_pet_affine_safe.nii.gz")

sitk.WriteImage(warped_image, output_warped_path)
print(f"\n标准化的图像 已保存至: {output_warped_path}")
if not use_bspline:
    print("  (使用安全的仅仿射配准)")
warped_stats = print_stats(warped_image, os.path.basename(output_warped_path))

# 定量数值保护验证
print("\n=== PET定量数值保护验证 ===")
def compare_quantitative_values(original_img, warped_img, name):
    """比较原始图像和配准后图像的定量特性"""
    orig_stats = sitk.StatisticsImageFilter()
    warp_stats = sitk.StatisticsImageFilter()
    orig_stats.Execute(original_img)
    warp_stats.Execute(warped_img)
    
    print(f"{name}定量对比:")
    print(f"  原始图像均值: {orig_stats.GetMean():.6f}")
    print(f"  配准后均值: {warp_stats.GetMean():.6f}")
    print(f"  均值变化率: {((warp_stats.GetMean() - orig_stats.GetMean()) / orig_stats.GetMean() * 100):.2f}%")
    print(f"  原始图像最大值: {orig_stats.GetMaximum():.6f}")
    print(f"  配准后最大值: {warp_stats.GetMaximum():.6f}")
    
    # 评估定量保护效果
    mean_change_percent = abs((warp_stats.GetMean() - orig_stats.GetMean()) / orig_stats.GetMean() * 100)
    if mean_change_percent < 5:
        print(f"  ✓ 定量数值变化 < 5%，定量特性得到良好保护")
    elif mean_change_percent < 15:
        print(f"  ⚠ 定量数值变化 {mean_change_percent:.1f}%，可接受范围")
    else:
        print(f"  ❌ 定量数值变化 {mean_change_percent:.1f}%，可能存在问题")

compare_quantitative_values(original_moving_image, warped_image, "")

if use_bspline:
    # 计算B样条变形的雅可比行列式（使用原始图像空间）
    identity_transform = sitk.Transform()
    grid_image = sitk.PhysicalPointSource(sitk.sitkVectorFloat64, original_fixed_image.GetSize(), original_fixed_image.GetOrigin(), original_fixed_image.GetSpacing(), original_fixed_image.GetDirection())
    warped_grid_image = sitk.Resample(grid_image, original_fixed_image, final_transform, sitk.sitkLinear)
    displacement_field = warped_grid_image - grid_image
    jacobian_determinant_filter = sitk.DisplacementFieldJacobianDeterminantFilter()
    jacobian_image = jacobian_determinant_filter.Execute(displacement_field)
    output_jacobian_path = os.path.join(output_folder_path, "jacobian.nii.gz")
    print(f"雅可比行列式图像 (jacobian) 已保存至: {output_jacobian_path}")
else:
    # 仿射变换的雅可比行列式是常数
    affine_matrix = final_affine_transform.GetMatrix()
    affine_det = abs(affine_matrix[0]*affine_matrix[4]*affine_matrix[8] + 
                    affine_matrix[1]*affine_matrix[5]*affine_matrix[6] + 
                    affine_matrix[2]*affine_matrix[3]*affine_matrix[7] - 
                    affine_matrix[2]*affine_matrix[4]*affine_matrix[6] - 
                    affine_matrix[1]*affine_matrix[3]*affine_matrix[8] - 
                    affine_matrix[0]*affine_matrix[5]*affine_matrix[7])
    jacobian_image = sitk.Image(original_fixed_image.GetSize(), sitk.sitkFloat64)
    jacobian_image.CopyInformation(original_fixed_image)
    jacobian_image = sitk.Add(jacobian_image, affine_det)
    output_jacobian_path = os.path.join(output_folder_path, "jacobian_affine.nii.gz")
    print(f"仿射雅可比行列式图像 (常数={affine_det:.6f}) 已保存至: {output_jacobian_path}")

sitk.WriteImage(jacobian_image, output_jacobian_path)
jacobian_stats = print_stats(jacobian_image, os.path.basename(output_jacobian_path))

# 验证雅可比行列式的合理性
def validate_jacobian(jacobian_stats, is_bspline_used):
    """验证雅可比行列式是否在合理范围内"""
    mean_jac = jacobian_stats.GetMean()
    min_jac = jacobian_stats.GetMinimum()
    max_jac = jacobian_stats.GetMaximum()
    
    print(f"\n雅可比行列式验证:")
    if is_bspline_used:
        print(f"  理想范围: 0.5 - 2.0 (正常非线性变形)")
        print(f"  实际范围: {min_jac:.6f} - {max_jac:.6f}")
        print(f"  平均值: {mean_jac:.6f} (应接近1.0)")
        
        is_valid = (mean_jac > 0.7 and mean_jac < 1.3 and 
                   min_jac > 0.1 and max_jac < 5.0)
        
        if not is_valid:
            print(f"  [警告] 雅可比行列式不在正常范围内，可能存在过度配准！")
            print(f"  建议: 减小B样条网格密度或增加正则化")
        else:
            print(f"  [正常] 雅可比行列式在合理范围内")
    else:
        print(f"  仿射变换雅可比行列式: {mean_jac:.6f} (常数)")
        print(f"  理想范围: 0.5 - 2.0 (仿射变换)")
        
        is_valid = (mean_jac > 0.3 and mean_jac < 3.0)
        
        if not is_valid:
            print(f"  [警告] 仿射变换的体积变化过大！")
        else:
            print(f"  [正常] 仿射变换的体积变化在合理范围内")
    
    return is_valid

is_jacobian_valid = validate_jacobian(jacobian_stats, use_bspline)

jacobian_image_casted = sitk.Cast(jacobian_image, warped_image.GetPixelID())
modulated_image = warped_image * jacobian_image_casted

# 根据配准方法调整调制图像文件名
if use_bspline:
    output_modulated_warped_path = os.path.join(output_folder_path, "mw_pet.nii.gz")
else:
    output_modulated_warped_path = os.path.join(output_folder_path, "mw_pet_affine_safe.nii.gz")

sitk.WriteImage(modulated_image, output_modulated_warped_path)
print(f"调制后的图像 已保存至: {output_modulated_warped_path}")
if not use_bspline:
    print("  (基于安全的仿射配准)")
modulated_stats = print_stats(modulated_image, os.path.basename(output_modulated_warped_path))

print("\n--- PET定量保护的SPM风格智能配准完成 ---")
print("🔒 关键特性：原始PET定量数值得到完全保护")
print("\n生成的文件：")
print(f"  - w_pet_affine_only.nii.gz: 仅仿射配准结果 (保持原始定量数值)")
if use_bspline:
    print(f"  - w_pet.nii.gz: 完整非线性标准化图像 (保持原始定量数值)")
    print(f"  - mw_pet.nii.gz: 调制标准化图像 (保持原始定量数值)")
    print(f"  - jacobian.nii.gz: B样条雅可比行列式 (体积变化信息)")
else:
    print(f"  - w_pet_affine_safe.nii.gz: 安全的仿射标准化图像 (保持原始定量数值)")
    print(f"  - mw_pet_affine_safe.nii.gz: 安全的仿射调制图像 (保持原始定量数值)")
    print(f"  - jacobian_affine.nii.gz: 仿射雅可比行列式 (常数)")

print(f"\n使用的配准方法: {'仿射 + B样条非线性' if use_bspline else '仅仿射 (安全模式)'}")
print("配准策略：归一化副本用于优化，原始图像用于最终变换")

print("\n配准质量评估:")
if 'is_bspline_valid' in locals() and 'is_jacobian_valid' in locals():
    if use_bspline and is_bspline_valid and is_jacobian_valid:
        print("  ✓ 非线性配准质量良好，变形在合理范围内")
    elif not use_bspline and is_jacobian_valid:
        print("  ✓ 仿射配准成功，自动避免了过度配准")
    else:
        print("  ⚠ 配准结果需要注意:")
        if use_bspline and not is_bspline_valid:
            print("    - B样条变形仍然过大")
        if not is_jacobian_valid:
            print("    - 雅可比行列式异常")

print(f"\n配准流程：定量保护预处理 → 仿射变换{' → 智能B样条选择' if use_bspline else ' → 安全退化'} → 原始图像变换 → 体积调制")
print("此智能流程模拟了SPM的spatial normalization功能，并完全保护PET的定量特性。")