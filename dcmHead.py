import os
import sys
import argparse
from typing import Optional

try:
    import pydicom
    from pydicom.errors import InvalidDicomError
except Exception:
    print("[ERROR] 需要安装 pydicom: pip install pydicom")
    raise


def _ensure_str(value) -> str:
    if value is None:
        return ""
    if isinstance(value, bytes):
        try:
            return value.decode(errors="ignore")
        except Exception:
            return str(value)
    return str(value)


def _prefixed(original: str, prefix: str) -> str:
    original = original or ""
    return original if original.startswith(prefix) else f"{prefix}{original}".strip()


def update_dicom_text_fields(
    folder: str,
    series_fixed: str,
    protocol_prefix: str,
    study_prefix: str,
) -> int:
    """为目录内所有 DICOM 的文本字段处理：
    - SeriesDescription (0008,103E): 设置为固定值 series_fixed
    - ProtocolName (0018,1030): 以前缀 protocol_prefix 开头
    - StudyDescription (0008,1030): 以前缀 study_prefix 开头

    返回成功写入的文件数量。
    """
    if not os.path.isdir(folder):
        raise FileNotFoundError(f"目录不存在: {folder}")

    success = 0
    for name in sorted(os.listdir(folder)):
        if not name.lower().endswith(".dcm"):
            continue
        path = os.path.join(folder, name)

        try:
            ds = pydicom.dcmread(path, force=True)
        except InvalidDicomError:
            print(f"[WARN] 非有效 DICOM 文件，跳过: {path}")
            continue
        except Exception as e:
            print(f"[WARN] 读取失败，跳过: {path} ({e})")
            continue

        # SeriesDescription: 固定值
        ds.SeriesDescription = _ensure_str(series_fixed)

        # ProtocolName: 前缀
        protocol_original = _ensure_str(getattr(ds, "ProtocolName", ""))
        ds.ProtocolName = _prefixed(protocol_original, protocol_prefix)

        # StudyDescription: 前缀
        study_original = _ensure_str(getattr(ds, "StudyDescription", ""))
        ds.StudyDescription = _prefixed(study_original, study_prefix)

        try:
            ds.save_as(path)
            success += 1
        except Exception as e:
            print(f"[WARN] 写入失败，跳过: {path} ({e})")
            continue

    return success


def parse_args(argv: Optional[list] = None) -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description=(
            "批量更新 DICOM 文本字段：\n"
            "- SeriesDescription(0008,103E) 设为固定值\n"
            "- ProtocolName(0018,1030) 添加前缀\n"
            "- StudyDescription(0008,1030) 添加前缀"
        )
    )
    parser.add_argument(
        "--dir",
        dest="directory",
        type=str,
        default=os.path.join("li_yunqiao_PET", "LYQ", "Clear Brain PET"),
        help="DICOM 目录（默认: li_yunqiao_PET/LYQ/Clear Brain PET）",
    )
    parser.add_argument(
        "--series-fixed",
        dest="series_fixed",
        type=str,
        default="Head_TOF_AC_SC_GF_1.0",
        help="SeriesDescription 固定写入值（默认: Head_TOF_AC_SC_GF_1.0）",
    )
    parser.add_argument(
        "--protocol-prefix",
        dest="protocol_prefix",
        type=str,
        default="Head_STATIC",
        help="ProtocolName 前缀（默认: Head_STATIC）",
    )
    parser.add_argument(
        "--study-prefix",
        dest="study_prefix",
        type=str,
        default="Head_PETCT",
        help="StudyDescription 前缀（默认: Head_PETCT）",
    )
    return parser.parse_args(argv)


def main(argv: Optional[list] = None) -> None:
    args = parse_args(argv)

    folder = args.directory
    series_fixed = args.series_fixed
    protocol_prefix = args.protocol_prefix
    study_prefix = args.study_prefix

    print(f"目标目录: {folder}")
    print(f"SeriesDescription 固定值: {series_fixed}")
    print(f"ProtocolName 前缀: {protocol_prefix}")
    print(f"StudyDescription 前缀: {study_prefix}")

    updated = update_dicom_text_fields(folder, series_fixed, protocol_prefix, study_prefix)
    print(f"完成: 成功更新 {updated} 个 DICOM 文件的文本字段。")


if __name__ == "__main__":
    main(sys.argv[1:])
