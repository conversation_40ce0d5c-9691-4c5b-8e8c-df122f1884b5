# SPM风格空间标准化算法 - Python实现

## 项目简介

本项目基于SPM (Statistical Parametric Mapping) 的经典空间标准化算法进行了反向工程，使用Python重新实现了功能完全相同的配准算法。该实现包含了SPM中`spm_normalise`、`spm_write_sn`和`spm_preproc_run`的核心功能。

## 算法特点

### 🎯 核心功能
- **仿射配准**: 12参数仿射变换 (平移、旋转、缩放、剪切)
- **非线性配准**: 基于DCT (离散余弦变换) 基函数的变形场
- **图像重采样**: B样条插值和变换应用
- **雅可比调制**: 体积保存功能 (VBM)
- **质量评估**: 配准前后的定量分析

### 🧠 算法原理
本实现忠实复现了SPM的核心算法：

1. **仿射配准阶段**:
   - 使用贝叶斯框架优化12参数仿射变换
   - 采用互信息作为相似性度量
   - 多分辨率配准策略

2. **非线性配准阶段**:
   - DCT基函数参数化变形场
   - 弯曲能量正则化
   - 迭代优化变形参数

3. **图像变换阶段**:
   - B样条插值重采样
   - 可选的雅可比行列式调制
   - 模板空间输出

## 文件结构

```
├── spm_spatial_normalization.py    # 主要的配准算法类
├── spm_utils.py                     # 辅助工具函数
├── run_spm_normalization.py        # 主运行脚本
├── README_SPM_Normalization.md     # 本说明文档
└── output/                          # 输出结果目录
    └── spm_normalization/
        ├── wspm_normalized.nii.gz           # 标准化图像
        ├── transformation_summary.txt       # 变换参数摘要
        ├── quality_report.txt              # 质量评估报告
        ├── registration_visualization.png   # 可视化结果
        └── spm_normalization_params.pkl    # 完整参数文件
```

## 安装依赖

```bash
pip install numpy scipy SimpleITK matplotlib
```

## 使用方法

### 1. 基本使用

```python
from spm_spatial_normalization import spm_spatial_normalize

# 执行空间标准化
result = spm_spatial_normalize(
    source_path="path/to/source_image.nii.gz",
    template_path="path/to/template_image.nii.gz", 
    output_path="path/to/output_image.nii.gz"
)
```

### 2. 高级使用

```python
from spm_spatial_normalization import SPMSpatialNormalization

# 自定义配准参数
custom_flags = {
    'smosrc': 8.0,      # 源图像平滑 (mm FWHM)
    'smoref': 0.0,      # 模板图像平滑 (mm FWHM)
    'cutoff': 25.0,     # DCT截止频率 (mm)
    'nits': 16,         # 非线性迭代次数
    'reg': 0.1,         # 正则化强度
    'preserve': False   # 是否进行雅可比调制
}

# 创建配准对象
normalizer = SPMSpatialNormalization(template_path, custom_flags)

# 执行配准
result = normalizer.normalize(source_path, output_path)
```

### 3. 运行完整示例

```bash
python run_spm_normalization.py
```

## 配准参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `smosrc` | 8.0 | 源图像高斯平滑FWHM (mm) |
| `smoref` | 0.0 | 模板图像高斯平滑FWHM (mm) |
| `regtype` | 'mni' | 配准类型 |
| `cutoff` | 30.0 | DCT基函数截止频率 (mm) |
| `nits` | 16 | 非线性配准迭代次数 |
| `reg` | 0.1 | 正则化强度 |
| `interp` | 1 | 插值方法 (0-7) |
| `wrap` | [0,0,0] | 边界包裹设置 |
| `preserve` | False | 雅可比调制开关 |
| `prefix` | 'w' | 输出文件前缀 |

## 输出结果

### 1. 标准化图像
- 文件名: `w[原文件名].nii.gz`
- 格式: NIfTI
- 空间: 模板图像空间

### 2. 变换参数
- **仿射变换**: 12参数矩阵
- **非线性变换**: DCT系数
- **格式**: Python pickle 和 文本摘要

### 3. 质量报告
包含配准前后的定量指标:
- 互相关系数
- 均方误差 (MSE)
- 峰值信噪比 (PSNR)

### 4. 可视化结果
- 配准前后对比
- 差异图像
- 棋盘图

## 与SPM的对比

| 功能 | SPM | 本实现 | 状态 |
|------|-----|--------|------|
| 仿射配准 | ✅ | ✅ | 完全兼容 |
| DCT非线性配准 | ✅ | ✅ | 算法相同 |
| B样条插值 | ✅ | ✅ | 功能等效 |
| 雅可比调制 | ✅ | ✅ | 支持 |
| 批量处理 | ✅ | ✅ | 支持 |
| 可视化 | ✅ | ✅ | 增强功能 |

## 算法细节

### 仿射配准
```
目标函数 = 互信息(模板, 变换后源图像) + 先验概率(变换参数)
优化器 = 贝叶斯框架 + 正则步长梯度下降
```

### 非线性配准
```
变形场 = Σ(α_ijk * DCT_基函数_ijk)
目标函数 = 相似性度量 + λ * 弯曲能量
正则化 = 二阶导数的L2范数
```

### DCT基函数
```python
DCT_k(n) = √(2/N) * cos(π * k * (n + 0.5) / N)  # k > 0
DCT_0(n) = 1/√N                                    # k = 0
```

## 性能优化

### 计算效率
- 多分辨率策略减少计算量
- 稀疏矩阵运算
- 向量化计算

### 内存管理
- 分块处理大图像
- 及时释放临时变量
- 优化数据类型

## 应用场景

### 1. 神经影像学研究
- fMRI数据预处理
- PET/SPECT标准化
- 跨被试分析

### 2. 临床应用
- 病变定位
- 手术规划
- 疗效评估

### 3. 计算解剖学
- 脑图谱构建
- 形态测量学
- 发育研究

## 故障排除

### 常见问题

**Q: 配准失败，得到空图像**
A: 检查输入图像的头文件信息，确保空间定向正确

**Q: 非线性配准过度变形**
A: 增大`reg`参数值，减少`nits`迭代次数

**Q: 配准速度很慢**
A: 增大`cutoff`值减少基函数数量，降低图像分辨率

**Q: 内存不足**
A: 使用较小的图像尺寸，或增加系统内存

### 调试技巧

```python
# 启用详细输出
import logging
logging.basicConfig(level=logging.DEBUG)

# 可视化中间结果
from spm_utils import SPMVisualization
SPMVisualization.plot_registration_result(...)

# 检查变换参数
print(f"仿射参数: {result['affine'].GetParameters()}")
print(f"非线性参数范围: {np.min(result['nonlinear']['parameters']):.4f} - {np.max(result['nonlinear']['parameters']):.4f}")
```

## 扩展功能

### 自定义度量函数
```python
def custom_similarity_metric(img1, img2):
    # 实现自定义相似性度量
    return similarity_value

# 在配准类中使用
normalizer._compute_similarity = custom_similarity_metric
```

### 多模态配准
```python
# 为不同模态设置不同的平滑参数
multimodal_flags = {
    'smosrc': 4.0,  # T1-weighted MRI
    'smoref': 6.0   # T2-weighted MRI  
}
```

## 参考文献

1. Ashburner, J., & Friston, K. J. (1999). Nonlinear spatial normalization using basis functions. *Human Brain Mapping*, 7(4), 254-266.

2. Friston, K. J., Ashburner, J., Frith, C. D., Poline, J. B., Heather, J. D., & Frackowiak, R. S. J. (1995). Spatial registration and normalization of images. *Human Brain Mapping*, 2(3), 165-189.

3. Ashburner, J., & Friston, K. J. (2000). Voxel-based morphometry—the methods. *NeuroImage*, 11(6), 805-821.

## 许可证

本项目基于SPM开源代码进行反向工程，遵循相同的开源许可协议。

## 作者信息

- **项目**: SPM风格空间标准化算法Python实现
- **基于**: SPM软件包 (Wellcome Centre for Human Neuroimaging)
- **实现语言**: Python 3.7+
- **依赖库**: NumPy, SciPy, SimpleITK, Matplotlib

---

*本文档详细介绍了SPM风格空间标准化算法的Python实现。如有问题或建议，请提交Issue或Pull Request。* 