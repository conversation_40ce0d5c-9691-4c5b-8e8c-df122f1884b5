import SimpleITK as sitk
import numpy as np
import os
import csv
import glob

def read_dicom_series(dicom_folder):
    """
    读取DICOM系列并转换为SimpleITK图像
    
    参数:
    dicom_folder (str): 包含DICOM文件的文件夹路径
    
    返回:
    sitk.Image: 转换后的3D图像
    """
    print(f"正在读取DICOM系列: {dicom_folder}")
    
    # 获取DICOM系列
    reader = sitk.ImageSeriesReader()
    dicom_names = reader.GetGDCMSeriesFileNames(dicom_folder)
    
    if not dicom_names:
        # 如果GetGDCMSeriesFileNames失败，尝试手动获取.dcm文件
        print("使用GDCM系列读取器失败，尝试手动读取DICOM文件...")
        dcm_files = glob.glob(os.path.join(dicom_folder, "*.dcm"))
        if not dcm_files:
            raise ValueError(f"在文件夹 {dicom_folder} 中未找到DICOM文件")
        dicom_names = sorted(dcm_files)
    
    print(f"找到 {len(dicom_names)} 个DICOM文件")
    
    reader.SetFileNames(dicom_names)
    reader.MetaDataDictionaryArrayUpdateOn()
    reader.LoadPrivateTagsOn()
    
    try:
        image = reader.Execute()
        print(f"成功读取DICOM系列，图像尺寸: {image.GetSize()}")
        print(f"像素间距: {image.GetSpacing()}")
        print(f"图像原点: {image.GetOrigin()}")
        return image
    except Exception as e:
        print(f"读取DICOM系列时出错: {e}")
        raise

def convert_dicom_to_nifti(dicom_folder, output_nifti_path):
    """
    将DICOM系列转换为NIfTI格式
    
    参数:
    dicom_folder (str): 包含DICOM文件的文件夹路径
    output_nifti_path (str): 输出NIfTI文件的路径
    
    返回:
    str: 转换后的NIfTI文件路径
    """
    print("--- 开始DICOM到NIfTI转换 ---")
    
    # 读取DICOM系列
    image = read_dicom_series(dicom_folder)
    
    # 确保输出目录存在
    output_dir = os.path.dirname(output_nifti_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 保存为NIfTI格式
    print(f"正在保存NIfTI文件: {output_nifti_path}")
    sitk.WriteImage(image, output_nifti_path)
    print("DICOM到NIfTI转换完成！")
    
    return output_nifti_path

def load_aal_mapping(aal_txt_path):
    """
    读取 AAL-Merged.txt 文件, 返回 {label_id: region_name} 的映射字典。
    若文件不存在, 返回空映射并在控制台提示。
    """
    mapping = {}
    if not os.path.exists(aal_txt_path):
        print(f"警告: 未找到 AAL 标签文件 {aal_txt_path}, 将使用数字标签。")
        return mapping

    with open(aal_txt_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            parts = line.split('\t')
            if len(parts) < 3:
                continue
            try:
                label_id = int(parts[2])
            except ValueError:
                continue
            region_name = parts[1]
            mapping[label_id] = region_name
    return mapping

def perform_quantitative_analysis_and_save_to_csv(pet_path, aal_path, output_csv_path):
    """
    对PET图像进行基于AAL模板的定量化区域分析，并将结果保存为CSV文件。

    参数:
    pet_path (str): 与AAL对齐后的PET nifti图像的路径。
    aal_path (str): AAL模板 nifti文件的路径。
    output_csv_path (str): 输出的CSV文件的保存路径。
    """
    print("--- 开始进行定量分析 ---")
    
    try:
        # --- 步骤 1: 加载图像 ---
        pet_image = sitk.ReadImage(pet_path, sitk.sitkFloat32)
        aal_image = sitk.ReadImage(aal_path, sitk.sitkUInt32)
        print("1. 成功加载PET图像和AAL模板。")

        # --- 步骤 2: 验证图像对齐 ---
        if pet_image.GetSize() != aal_image.GetSize():
            print("\n警告：PET图像和AAL模板的尺寸不匹配。")
            print("需要先将PET图像配准到AAL模板空间。")
            print("正在尝试将PET图像重采样到AAL模板空间...")
            
            # 重采样PET图像到AAL模板空间
            resampler = sitk.ResampleImageFilter()
            resampler.SetReferenceImage(aal_image)
            resampler.SetInterpolator(sitk.sitkLinear)
            resampler.SetDefaultPixelValue(0)
            pet_image = resampler.Execute(pet_image)
            print("PET图像已重采样到AAL模板空间。")

        # --- 步骤 3: 使用LabelStatisticsImageFilter进行核心分析 ---
        label_stats_filter = sitk.LabelStatisticsImageFilter()
        label_stats_filter.Execute(pet_image, aal_image)
        print("2. 已成功计算每个AAL脑区的PET统计数据。")

        # === 加载 AAL 标签映射 ===
        aal_txt_path = os.path.join(os.path.dirname(aal_path), "AAL-Merged.txt")
        label_mapping = load_aal_mapping(aal_txt_path)
        # 新增：自定义标签映射（如80为白质）
        custom_label_mapping = {80: "White Matter"}
        # 合并自定义标签映射（优先AAL-Merged.txt）
        label_mapping = {**custom_label_mapping, **label_mapping}

        # --- 步骤 4: 将结果写入CSV文件 ---
        print(f"3. 正在将分析结果写入CSV文件: {output_csv_path}")
        
        # 确保输出目录存在
        output_dir = os.path.dirname(output_csv_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 使用'with'语句确保文件能被正确关闭
        with open(output_csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            # 创建一个CSV写入对象
            csv_writer = csv.writer(csvfile)
            
            # 写入表头 (Header)
            header = ['AAL_Region_Name', 'Mean_PET_Value', 'Standard_Deviation', 'Voxel_Count', 'Volume_ccm']
            csv_writer.writerow(header)
            
            # 遍历所有在AAL模板中存在的标签
            for label_value in sorted(label_stats_filter.GetLabels()):
                if label_value == 0:  # 忽略背景标签
                    continue
                
                # 获取统计数据
                mean_val = label_stats_filter.GetMean(label_value) / 1000  # 将平均值除以1000
                std_dev_val = label_stats_filter.GetSigma(label_value)
                count_val = label_stats_filter.GetCount(label_value)

                # === 计算体积（立方厘米） ===
                # 使用PET图像的体素间距，单位mm
                spacing = pet_image.GetSpacing()  # (x, y, z) in mm
                voxel_volume_mm3 = spacing[0] * spacing[1] * spacing[2]
                volume_ccm = (count_val * voxel_volume_mm3) / 1000.0  # 1 cm^3 = 1000 mm^3
                
                # 将标签数字映射为名称, 若无映射则退回数字
                region_name = label_mapping.get(label_value, str(label_value))
                # 将数据写入一行
                data_row = [region_name, mean_val, std_dev_val, count_val, volume_ccm]
                csv_writer.writerow(data_row)
        
        print("   写入完成！")

        # --- 步骤 5: 额外检查 (结果打印在屏幕上) ---
        print("\n--- 额外分析：检查被AAL模板忽略的PET信号 ---")
        pet_np = sitk.GetArrayViewFromImage(pet_image)
        aal_np = sitk.GetArrayViewFromImage(aal_image)
        significant_pet_mask = pet_np > (pet_np.mean() * 1.5) # 定义一个动态的"显著"阈值
        outside_aal_mask = aal_np == 0
        missed_voxels_mask = significant_pet_mask & outside_aal_mask
        num_missed_voxels = np.sum(missed_voxels_mask)
        
        if num_missed_voxels > 0:
            missed_pet_values = pet_np[missed_voxels_mask]
            print(f"发现在AAL模板之外，存在 {num_missed_voxels} 个具有显著信号的体素。")
            print(f"这些被忽略的体素的PET信号平均值为: {np.mean(missed_pet_values) / 1000:.2f}")  # 将平均值除以1000
        else:
            print("未在AAL模板之外发现显著的PET信号。模板覆盖良好。")
            
        print("\n--- 所有处理已完成！ ---")
        print(f"定量分析结果已成功保存到 '{output_csv_path}'。")

    except Exception as e:
        print(f"\n处理过程中发生严重错误: {e}")

def analyze_dicom_pet_with_aal(dicom_folder, aal_path, output_csv_path, temp_nifti_path=None):
    """
    完整的DICOM PET定量分析流程
    
    参数:
    dicom_folder (str): DICOM文件夹路径
    aal_path (str): AAL模板路径
    output_csv_path (str): 输出CSV文件路径
    temp_nifti_path (str, optional): 临时NIfTI文件路径，如果不提供会自动生成
    """
    print("=== 开始完整的DICOM PET定量分析流程 ===")
    
    # 如果没有提供临时NIfTI路径，自动生成一个
    if temp_nifti_path is None:
        temp_nifti_path = os.path.join(os.path.dirname(output_csv_path), "temp_pet_from_dicom.nii.gz")
    
    try:
        # 步骤1: 转换DICOM为NIfTI
        converted_nifti = convert_dicom_to_nifti(dicom_folder, temp_nifti_path)
        
        # 步骤2: 执行定量分析
        perform_quantitative_analysis_and_save_to_csv(converted_nifti, aal_path, output_csv_path)
        
        # 步骤3: 清理临时文件（可选）
        # 如果您想保留转换后的NIfTI文件，请注释掉下面的代码
        if os.path.exists(temp_nifti_path):
            print(f"清理临时文件: {temp_nifti_path}")
            # os.remove(temp_nifti_path)  # 取消注释以删除临时文件
        
        print("\n=== 完整分析流程已完成！ ===")
        
    except Exception as e:
        print(f"\n分析流程中发生错误: {e}")
        # 如果出错，清理可能存在的临时文件
        if temp_nifti_path and os.path.exists(temp_nifti_path):
            try:
                os.remove(temp_nifti_path)
                print(f"已清理临时文件: {temp_nifti_path}")
            except:
                pass

# --- 主程序入口 ---
if __name__ == '__main__':
    
    # ==========================================================
    # --- 请在这里修改您的文件路径 ---
    # ==========================================================
    
    # 1. 您的DICOM文件夹路径
    dicom_folder_path = "li_yunqiao_PET/LYQ/Clear Brain PET"
    
    # 2. 您的AAL模板路径
    aal_file = "output/test_PET_spm_style/AAL_intersected_pureGM_p05.nii.gz"
    
    # 3. 您希望保存的CSV表格的文件名
    output_csv_file = "output/test_PET_spm_style/pet_dicom_aal_quantitative_results.csv"
    
    # 4. 临时NIfTI文件路径（可选）
    temp_nifti_file = "output/test_PET_spm_style/pet_converted_from_dicom.nii.gz"
    
    # ==========================================================
    
    # 检查输入文件夹和AAL模板是否存在
    if not os.path.exists(dicom_folder_path):
        print(f"!!!错误：找不到DICOM文件夹: {dicom_folder_path}")
    elif not os.path.exists(aal_file):
        print(f"!!!错误：找不到AAL模板文件: {aal_file}")
    else:
        # 执行完整的分析流程
        analyze_dicom_pet_with_aal(dicom_folder_path, aal_file, output_csv_file, temp_nifti_file) 