import os
import sys
import argparse
import random
from typing import Optional

try:
    import pydicom
    from pydicom.errors import InvalidDicomError
except Exception as exc:  # pragma: no cover
    print("[ERROR] 需要安装 pydicom: pip install pydicom")
    raise


def generate_numeric_string(length: int = 12) -> str:
    """生成指定长度的纯数字字符串。"""
    digits = "0123456789"
    return "".join(random.choice(digits) for _ in range(length))


def set_study_id_for_folder(folder: str, study_id: str) -> int:
    """为给定文件夹下的所有 .dcm 文件设置相同的 StudyID。

    返回成功写入的文件数量。
    """
    if not os.path.isdir(folder):
        raise FileNotFoundError(f"目录不存在: {folder}")

    count_success = 0
    for name in sorted(os.listdir(folder)):
        if not name.lower().endswith(".dcm"):
            continue
        path = os.path.join(folder, name)
        try:
            ds = pydicom.dcmread(path, force=True)
        except InvalidDicomError:
            print(f"[WARN] 非有效 DICOM 文件，跳过: {path}")
            continue
        except Exception as e:  # 其他读取错误
            print(f"[WARN] 读取失败，跳过: {path} ({e})")
            continue

        # 设置 StudyID (0020,0010)
        ds.StudyID = str(study_id)

        try:
            ds.save_as(path)
            count_success += 1
        except Exception as e:
            print(f"[WARN] 写入失败，跳过: {path} ({e})")
            continue

    return count_success


def parse_args(argv: Optional[list] = None) -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description="为指定目录内的 DICOM 序列统一设置相同的 StudyID (0020,0010)。"
    )
    parser.add_argument(
        "--dir",
        dest="directory",
        type=str,
        default=os.path.join("li_yunqiao_PET", "LYQ", "Clear Brain PET"),
        help="DICOM 目录（默认: li_yunqiao_PET/LYQ/Clear Brain PET）",
    )
    parser.add_argument(
        "--study-id",
        dest="study_id",
        type=str,
        default=None,
        help="指定要写入的 StudyID；若不提供则自动随机生成纯数字。",
    )
    parser.add_argument(
        "--length",
        dest="length",
        type=int,
        default=12,
        help="随机 StudyID 的长度（默认 12，仅在未指定 --study-id 时生效）",
    )
    return parser.parse_args(argv)


def main(argv: Optional[list] = None) -> None:
    args = parse_args(argv)

    # 生成或采用提供的 StudyID
    if args.study_id is None or len(str(args.study_id).strip()) == 0:
        study_id = generate_numeric_string(max(1, int(args.length)))
        auto_generated = True
    else:
        study_id = str(args.study_id)
        auto_generated = False

    print(f"目标目录: {args.directory}")
    print(f"StudyID: {study_id} ({'自动生成' if auto_generated else '用户指定'})")

    updated = set_study_id_for_folder(args.directory, study_id)
    print(f"完成: 成功更新 {updated} 个 DICOM 文件的 StudyID。")


if __name__ == "__main__":
    main(sys.argv[1:])
