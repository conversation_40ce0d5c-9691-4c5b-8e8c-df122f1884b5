import SimpleITK as sitk
import os

# 路径设置
template_path = "csf_prob_map.nii.gz"  # 模板
pet_path = "li_yunqiao_PET/NIFTI_Output_Folder/Series.nii.gz"  # 源PET
affine_path = "output/my_result/AAL-Merged/final_affine_transform.tfm"
output_inverse_path = "output/my_result/AAL-Merged/csf_template_to_pet_inverse_affineonly.nii.gz"

# 读取图像
template_img = sitk.ReadImage(template_path, sitk.sitkFloat32)
pet_img = sitk.ReadImage(pet_path, sitk.sitkFloat32)

# 对于标签图像，需要特殊处理以保持整型标签
print(f"模板图像像素类型: {template_img.GetPixelIDTypeAsString()}")
print(f"模板图像数值范围: {sitk.GetArrayFromImage(template_img).min()} - {sitk.GetArrayFromImage(template_img).max()}")

# 读取变换并取逆
affine = sitk.ReadTransform(affine_path)
affine_inv = affine.GetInverse()

# 组合逆变换（先B样条逆，再仿射逆）
composite_inv = sitk.CompositeTransform(3)
composite_inv.AddTransform(affine_inv)

# 逆变换重采样 - 概率图像用线性插值
resampler = sitk.ResampleImageFilter()
resampler.SetReferenceImage(pet_img)
resampler.SetInterpolator(sitk.sitkLinear)  # 概率图像用线性插值
resampler.SetDefaultPixelValue(0)
resampler.SetTransform(affine_inv)
template_in_pet_space = resampler.Execute(template_img)

# 不需要Cast为整型，保持float32
# 保存结果
sitk.WriteImage(template_in_pet_space, output_inverse_path)
print(f"概率模板已通过仿射逆变换映射到PET空间，结果保存为: {output_inverse_path}")
print(f"变换后图像数值范围: {sitk.GetArrayFromImage(template_in_pet_space).min()} - {sitk.GetArrayFromImage(template_in_pet_space).max()}")
print("✓ 使用线性插值，概率值保持连续")
