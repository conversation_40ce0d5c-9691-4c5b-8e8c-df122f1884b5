import SimpleITK as sitk
import os
import sys

# --- 步骤 1: 设置文件路径 ---
# !! 重要 !!
# 请将下面两个路径变量修改为您电脑上的实际路径

# 输入：包含您所有.dcm文件的文件夹的路径
# 例如: "D:/MyData/Patient1/T1_Scan_DICOMs"
dicom_folder_path = "./2"

# 输出：您希望保存NIfTI文件的文件夹路径
# 例如: "D:/MyData/Patient1/NIFTI_Output"
# NIfTI文件名会自动根据内容生成，建议只指定文件夹
output_folder_path = "./output/2"


# --- 脚本主体 (通常无需修改以下部分) ---

# --- 检查路径 ---
if not os.path.isdir(dicom_folder_path):
    print(f"错误: 输入的DICOM文件夹不存在或不是一个文件夹: {dicom_folder_path}")
    sys.exit(1)

# 如果输出文件夹不存在，则创建它
if not os.path.exists(output_folder_path):
    print(f"输出文件夹不存在，正在创建: {output_folder_path}")
    os.makedirs(output_folder_path)

print(f"正在扫描DICOM文件夹: {dicom_folder_path}")

# --- 使用ImageSeriesReader的智能扫描功能 ---
# GetGDCMSeriesIDs会扫描文件夹并返回所有找到的序列的唯一ID (SeriesInstanceUID)
try:
    series_ids = sitk.ImageSeriesReader.GetGDCMSeriesIDs(dicom_folder_path)
    if not series_ids:
        print(f"错误: 在文件夹 {dicom_folder_path} 中没有找到任何DICOM序列。")
        print("请检查文件夹路径是否正确，以及文件夹内是否包含有效的.dcm文件。")
        sys.exit(1)
except Exception as e:
    print(f"读取DICOM序列时发生错误: {e}")
    sys.exit(1)


print(f"在文件夹中找到了 {len(series_ids)} 个扫描序列。")
if len(series_ids) > 1:
    print("警告: 文件夹中存在多个扫描序列。脚本将尝试转换每一个序列。")

# --- 遍历找到的所有序列并进行转换 ---
for i, series_id in enumerate(series_ids):
    print(f"\n--- 正在处理序列 {i+1}/{len(series_ids)} ---")
    print(f"序列ID: {series_id}")

    # 获取当前序列ID对应的所有dcm文件名列表
    # SimpleITK会自动按切片位置(Image Position Patient)对它们进行排序
    series_file_names = sitk.ImageSeriesReader.GetGDCMSeriesFileNames(dicom_folder_path, series_id)
    print(f"此序列包含 {len(series_file_names)} 个文件。")

    # --- 读取和写入 ---
    reader = sitk.ImageSeriesReader()
    reader.SetFileNames(series_file_names)

    try:
        print("正在将DICOM切片堆叠为3D图像...")
        image = reader.Execute()
        
        # 从DICOM的SeriesDescription或ProtocolName中获取一些信息来命名文件
        # 这比随机命名好得多
        file_name_tag = "Series"
        if "0008|103e" in image: # Series Description
             file_name_tag = image.GetMetaData("0008|103e").strip()
        elif "0018|1030" in image: # Protocol Name
             file_name_tag = image.GetMetaData("0018|1030").strip()

        # 清理文件名中的无效字符
        safe_file_name = "".join(c for c in file_name_tag if c.isalnum() or c in (' ', '_')).rstrip()
        safe_file_name = safe_file_name.replace(' ', '_') if safe_file_name else f"Series_{i+1}"
        
        output_nifti_path = os.path.join(output_folder_path, f"{safe_file_name}.nii.gz")
        
        print(f"正在将3D图像写入NIfTI文件: {output_nifti_path}")
        # 使用 .nii.gz 后缀，SimpleITK会自动进行压缩，这是标准做法
        sitk.WriteImage(image, output_nifti_path)
        print("写入成功！")

    except Exception as e:
        print(f"处理序列 {series_id} 时发生错误: {e}")
        continue

print("\n--- 所有操作完成 ---")