import os
try:
    import nibabel as nib
except ImportError:
    nib = None
try:
    import SimpleITK as sitk
except ImportError:
    sitk = None

pet_path = "normalization/PET.nii.gz"
aal_path = "PMOD-VOI/AAL-VOIs-NEURO.nii.gz"

def print_sitk_info(path):
    if sitk is None:
        print(f"SimpleITK 未安装，无法读取 {path}")
        return
    img = sitk.ReadImage(path)
    print(f"\nSimpleITK: {path}")
    print("  Origin:", img.GetOrigin())
    print("  Spacing:", img.GetSpacing())
    print("  Direction:", img.GetDirection())
    print("  Size:", img.GetSize())

def print_nibabel_info(path):
    if nib is None:
        print(f"nibabel 未安装，无法读取 {path}")
        return
    img = nib.load(path)
    print(f"\nnibabel: {path}")
    print("  shape:", img.shape)
    print("  affine:\n", img.affine)
    hdr = img.header
    print("  sform_code:", hdr.get('sform_code', None))
    print("  qform_code:", hdr.get('qform_code', None))
    print("  sform_affine:\n", img.get_sform())
    print("  qform_affine:\n", img.get_qform())
    print("  zooms (spacing):", hdr.get_zooms())

def fix_aal_affine():
    if nib is None:
        print("nibabel 未安装，无法修正 AAL 模板空间信息")
        return
    pet_img = nib.load(pet_path)
    aal_img = nib.load(aal_path)
    aal_new = nib.Nifti1Image(aal_img.get_fdata(), pet_img.affine, header=pet_img.header)
    out_path = "PMOD-VOI/AAL-VOIs-NEURO_fixed.nii.gz"
    nib.save(aal_new, out_path)
    print(f"已保存修正空间信息的AAL模板到: {out_path}")

def main():
    for path in [pet_path, aal_path]:
        print_sitk_info(path)
        print_nibabel_info(path)

if __name__ == "__main__":
    main()
    fix_aal_affine()
