#!/usr/bin/env python3
"""
快速测试豪斯多夫距离计算
"""

import SimpleITK as sitk
import numpy as np
import os

def quick_test():
    """快速测试单个标签的豪斯多夫距离"""
    
    # 文件路径
    file1_path = 'output/pmod_result/N30R83_1MM_2/parcel_s2_Brain_segments__Hammers_N30R83_1mm_atlas_region_labels____NORMALIZED_to_TOF_AC_SC_GF_MF_1_0____Atlas_in_Functional_space_Hammers_N30R83_1MM.nii'
    file2_path = 'output/my_result/Hammers-N30R83-1MM/Hammers-N30R83-1MM_withWM.nii.gz'
    
    print("快速豪斯多夫距离测试")
    
    try:
        # 读取图像
        gt_img = sitk.ReadImage(file1_path, sitk.sitkUInt16)
        seg_img = sitk.ReadImage(file2_path, sitk.sitkUInt16)
        
        # 测试标签1
        label = 1
        print(f"\n测试标签 {label}:")
        
        # 创建单标签二值图像
        gt_label = sitk.BinaryThreshold(gt_img, lowerThreshold=float(label), 
                                      upperThreshold=float(label), 
                                      insideValue=1, outsideValue=0)
        seg_label = sitk.BinaryThreshold(seg_img, lowerThreshold=float(label), 
                                       upperThreshold=float(label), 
                                       insideValue=1, outsideValue=0)
        
        # 检查体素数
        gt_stats = sitk.StatisticsImageFilter()
        gt_stats.Execute(gt_label)
        seg_stats = sitk.StatisticsImageFilter()
        seg_stats.Execute(seg_label)
        
        print(f"金标准体素数: {int(gt_stats.GetSum())}")
        print(f"分割结果体素数: {int(seg_stats.GetSum())}")
        
        if gt_stats.GetSum() > 0 and seg_stats.GetSum() > 0:
            try:
                hausdorff_filter = sitk.HausdorffDistanceImageFilter()
                hausdorff_filter.Execute(gt_label, seg_label)
                hausdorff_distance = hausdorff_filter.GetHausdorffDistance()
                print(f"✅ 豪斯多夫距离: {hausdorff_distance:.4f}")
            except Exception as e:
                print(f"❌ 豪斯多夫距离计算失败: {e}")
                print(f"错误类型: {type(e).__name__}")
        else:
            print("❌ 标签为空，无法计算豪斯多夫距离")
            
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    quick_test()
