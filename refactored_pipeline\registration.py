import SimpleITK as sitk
import os

def affine_registration(moving_image_path, fixed_image_path, output_folder_path, 
                       histogram_bins=50, learning_rate=2.0, min_step=1e-6, 
                       iterations=300, relaxation_factor=0.8, 
                       shrink_factors=None, smoothing_sigmas=None):
    """
    对输入的moving和fixed图像进行仿射配准，并输出仿射变换和配准结果。
    
    Args:
        moving_image_path: 移动图像路径
        fixed_image_path: 固定图像路径
        output_folder_path: 输出文件夹路径
        histogram_bins: 直方图箱数
        learning_rate: 学习率
        min_step: 最小步长
        iterations: 迭代次数
        relaxation_factor: 松弛因子
        shrink_factors: 多分辨率收缩因子列表
        smoothing_sigmas: 多分辨率平滑因子列表
    """
    # 设置默认值
    if shrink_factors is None:
        shrink_factors = [8, 4, 2, 1]
    if smoothing_sigmas is None:
        smoothing_sigmas = [4, 2, 1, 0]
    
    if not all(os.path.exists(p) for p in [moving_image_path, fixed_image_path]):
        raise FileNotFoundError("找不到输入的数据文件或模板文件。请仔细检查路径。")
    if not os.path.isdir(output_folder_path):
        os.makedirs(output_folder_path)

    fixed_image = sitk.ReadImage(fixed_image_path, sitk.sitkFloat32)
    moving_image = sitk.ReadImage(moving_image_path, sitk.sitkFloat32)
    original_fixed_image = fixed_image
    original_moving_image = moving_image

    def create_registration_copy(image, image_name):
        stats = sitk.StatisticsImageFilter()
        stats.Execute(image)
        mean_val = stats.GetMean()
        if mean_val > 0:
            normalized = sitk.Divide(image, mean_val / 50.0)
            return normalized
        return image

    fixed_image = create_registration_copy(original_fixed_image, "固定图像")
    moving_image = create_registration_copy(original_moving_image, "移动图像")

    affine_registration = sitk.ImageRegistrationMethod()
    affine_registration.SetMetricAsMattesMutualInformation(numberOfHistogramBins=histogram_bins)
    affine_registration.SetMetricSamplingStrategy(affine_registration.NONE)
    affine_registration.SetInterpolator(sitk.sitkLinear)
    affine_registration.SetOptimizerAsRegularStepGradientDescent(
        learningRate=learning_rate, 
        minStep=min_step, 
        numberOfIterations=iterations, 
        relaxationFactor=relaxation_factor
    )
    affine_registration.SetOptimizerScalesFromPhysicalShift()
    affine_registration.SetShrinkFactorsPerLevel(shrink_factors)
    affine_registration.SetSmoothingSigmasPerLevel(smoothing_sigmas)
    affine_registration.SmoothingSigmasAreSpecifiedInPhysicalUnitsOn()
    initial_transform = sitk.CenteredTransformInitializer(fixed_image, moving_image, sitk.AffineTransform(3), sitk.CenteredTransformInitializerFilter.MOMENTS)
    affine_registration.SetInitialTransform(initial_transform)
    final_affine_transform = affine_registration.Execute(fixed_image, moving_image)
    affine_path = os.path.join(output_folder_path, "final_affine_transform.tfm")
    sitk.WriteTransform(final_affine_transform, affine_path)
    moving_image_affined_original = sitk.Resample(original_moving_image, original_fixed_image, final_affine_transform, sitk.sitkLinear, 0.0, original_moving_image.GetPixelID())
    affined_img_path = os.path.join(output_folder_path, "w_pet_affine_only.nii.gz")
    sitk.WriteImage(moving_image_affined_original, affined_img_path)
    return affine_path, affined_img_path 