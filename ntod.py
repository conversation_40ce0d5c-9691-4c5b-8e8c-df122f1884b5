#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NIfTI to DICOM Converter
将NIfTI格式的医学图像转换为DICOM格式

依赖库:
pip install nibabel pydicom numpy SimpleITK
"""

import os
import sys
import numpy as np
import nibabel as nib
from datetime import datetime
import argparse

# 尝试导入必要的库
try:
    import pydicom
    from pydicom.dataset import Dataset, FileDataset
    from pydicom.uid import generate_uid
except ImportError:
    print("错误: 请安装 pydicom 库: pip install pydicom")
    sys.exit(1)

try:
    import SimpleITK as sitk
    SITK_AVAILABLE = True
except ImportError:
    print("警告: SimpleITK 未安装，将使用基础转换方法")
    SITK_AVAILABLE = False


def nifti_to_dicom_sitk(nifti_path, output_dir, series_description="Converted from NIfTI"):
    """
    使用SimpleITK将NIfTI转换为DICOM (推荐方法)
    
    Args:
        nifti_path (str): NIfTI文件路径
        output_dir (str): 输出目录
        series_description (str): DICOM系列描述
    """
    if not SITK_AVAILABLE:
        raise ImportError("SimpleITK 未安装")
    
    # 读取NIfTI文件
    print(f"正在读取NIfTI文件: {nifti_path}")
    image = sitk.ReadImage(nifti_path)
    
    # 获取图像信息
    size = image.GetSize()
    spacing = image.GetSpacing()
    origin = image.GetOrigin()
    direction = image.GetDirection()
    
    print(f"图像尺寸: {size}")
    print(f"像素间距: {spacing}")
    print(f"原点: {origin}")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成唯一ID
    series_uid = generate_uid()
    study_uid = generate_uid()
    frame_of_reference_uid = generate_uid()
    
    # 获取当前时间
    dt = datetime.now()
    date_str = dt.strftime("%Y%m%d")
    time_str = dt.strftime("%H%M%S.%f")[:-3]
    
    # 转换每个切片
    num_slices = size[2] if len(size) > 2 else 1
    
    for i in range(num_slices):
        # 提取单个切片
        if len(size) > 2:
            slice_filter = sitk.ExtractImageFilter()
            slice_filter.SetSize([size[0], size[1], 0])
            slice_filter.SetIndex([0, 0, i])
            slice_image = slice_filter.Execute(image)
        else:
            slice_image = image
        
        # 转换为numpy数组
        array = sitk.GetArrayFromImage(slice_image)
        
        # 创建DICOM数据集
        file_meta = Dataset()
        file_meta.MediaStorageSOPClassUID = '1.2.840.10008.*******.1.7'  # Secondary Capture Image Storage
        file_meta.MediaStorageSOPInstanceUID = generate_uid()
        file_meta.ImplementationClassUID = generate_uid()
        file_meta.TransferSyntaxUID = '1.2.840.10008.1.2'  # Implicit VR Little Endian
        
        ds = FileDataset(f"slice_{i:04d}.dcm", {}, file_meta=file_meta, preamble=b"\0" * 128)
        
        # 设置必要的DICOM标签
        ds.PatientName = "Anonymous"
        ds.PatientID = "ANON001"
        ds.PatientBirthDate = ""
        ds.PatientSex = ""
        
        ds.StudyDate = date_str
        ds.StudyTime = time_str
        ds.StudyInstanceUID = study_uid
        ds.StudyID = "1"
        ds.StudyDescription = "NIfTI Conversion"
        
        ds.SeriesDate = date_str
        ds.SeriesTime = time_str
        ds.SeriesInstanceUID = series_uid
        ds.SeriesNumber = 1
        ds.SeriesDescription = series_description
        
        ds.InstanceNumber = i + 1
        ds.SOPInstanceUID = generate_uid()
        ds.SOPClassUID = '1.2.840.10008.*******.1.7'
        
        ds.Modality = "OT"  # Other
        ds.FrameOfReferenceUID = frame_of_reference_uid
        
        # 图像相关信息
        ds.ImageType = ["DERIVED", "SECONDARY"]
        ds.SamplesPerPixel = 1
        ds.PhotometricInterpretation = "MONOCHROME2"
        ds.Rows = array.shape[0]
        ds.Columns = array.shape[1]
        ds.BitsAllocated = 16
        ds.BitsStored = 16
        ds.HighBit = 15
        ds.PixelRepresentation = 0
        
        # 空间信息
        if len(spacing) >= 2:
            ds.PixelSpacing = [spacing[1], spacing[0]]  # 行间距, 列间距
        if len(spacing) >= 3:
            ds.SliceThickness = spacing[2]
        
        # 位置信息
        if len(size) > 2:
            slice_location = origin[2] + i * spacing[2]
            ds.SliceLocation = slice_location
            
            # 图像位置和方向
            ds.ImagePositionPatient = [origin[0], origin[1], slice_location]
            ds.ImageOrientationPatient = [direction[0], direction[1], direction[2], 
                                        direction[3], direction[4], direction[5]]
        
        # 转换像素数据
        array = array.astype(np.uint16)
        ds.PixelData = array.tobytes()
        
        # 保存DICOM文件
        output_filename = os.path.join(output_dir, f"slice_{i:04d}.dcm")
        ds.save_as(output_filename, write_like_original=False)
        print(f"已保存切片 {i+1}/{num_slices}: {output_filename}")
    
    print(f"转换完成！共生成 {num_slices} 个DICOM文件")
    return num_slices


def nifti_to_dicom_basic(nifti_path, output_dir, series_description="Converted from NIfTI"):
    """
    使用基础方法将NIfTI转换为DICOM
    
    Args:
        nifti_path (str): NIfTI文件路径
        output_dir (str): 输出目录
        series_description (str): DICOM系列描述
    """
    # 读取NIfTI文件
    print(f"正在读取NIfTI文件: {nifti_path}")
    nii = nib.load(nifti_path)
    data = nii.get_fdata()
    header = nii.header
    affine = nii.affine
    
    print(f"图像形状: {data.shape}")
    print(f"数据类型: {data.dtype}")
    print(f"像素尺寸: {header.get_zooms()}")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成唯一ID
    series_uid = generate_uid()
    study_uid = generate_uid()
    frame_of_reference_uid = generate_uid()
    
    # 获取当前时间
    dt = datetime.now()
    date_str = dt.strftime("%Y%m%d")
    time_str = dt.strftime("%H%M%S.%f")[:-3]
    
    # 处理数据维度
    if len(data.shape) == 2:
        data = data[:, :, np.newaxis]
    elif len(data.shape) > 3:
        print("警告: 检测到4D数据，仅使用第一个时间点")
        data = data[:, :, :, 0]
    
    num_slices = data.shape[2]
    pixel_spacing = header.get_zooms()
    
    # 转换每个切片
    for i in range(num_slices):
        slice_data = data[:, :, i]
        
        # 数据预处理
        slice_data = np.rot90(slice_data)  # 旋转以匹配DICOM坐标系
        slice_data = np.flipud(slice_data)  # 翻转以匹配DICOM坐标系
        
        # 转换到合适的数据类型
        slice_data = slice_data.astype(np.float64)
        
        # 缩放到16位整数范围
        if slice_data.max() <= 1.0:
            slice_data = slice_data * 65535
        elif slice_data.max() <= 255:
            slice_data = slice_data * 256
        
        slice_data = np.clip(slice_data, 0, 65535).astype(np.uint16)
        
        # 创建DICOM数据集
        file_meta = Dataset()
        file_meta.MediaStorageSOPClassUID = '1.2.840.10008.*******.1.7'
        file_meta.MediaStorageSOPInstanceUID = generate_uid()
        file_meta.ImplementationClassUID = generate_uid()
        file_meta.TransferSyntaxUID = '1.2.840.10008.1.2'
        
        ds = FileDataset(f"slice_{i:04d}.dcm", {}, file_meta=file_meta, preamble=b"\0" * 128)
        
        # 患者信息
        ds.PatientName = "Anonymous"
        ds.PatientID = "ANON001"
        ds.PatientBirthDate = ""
        ds.PatientSex = ""
        
        # 检查信息
        ds.StudyDate = date_str
        ds.StudyTime = time_str
        ds.StudyInstanceUID = study_uid
        ds.StudyID = "1"
        ds.StudyDescription = "NIfTI Conversion"
        
        # 系列信息
        ds.SeriesDate = date_str
        ds.SeriesTime = time_str
        ds.SeriesInstanceUID = series_uid
        ds.SeriesNumber = 1
        ds.SeriesDescription = series_description
        
        # 图像信息
        ds.InstanceNumber = i + 1
        ds.SOPInstanceUID = generate_uid()
        ds.SOPClassUID = '1.2.840.10008.*******.1.7'
        
        ds.Modality = "OT"
        ds.FrameOfReferenceUID = frame_of_reference_uid
        
        # 图像参数
        ds.ImageType = ["DERIVED", "SECONDARY"]
        ds.SamplesPerPixel = 1
        ds.PhotometricInterpretation = "MONOCHROME2"
        ds.Rows = slice_data.shape[0]
        ds.Columns = slice_data.shape[1]
        ds.BitsAllocated = 16
        ds.BitsStored = 16
        ds.HighBit = 15
        ds.PixelRepresentation = 0
        
        # 空间信息
        if len(pixel_spacing) >= 2:
            ds.PixelSpacing = [pixel_spacing[0], pixel_spacing[1]]
        if len(pixel_spacing) >= 3:
            ds.SliceThickness = pixel_spacing[2]
            ds.SliceLocation = i * pixel_spacing[2]
        
        # 像素数据
        ds.PixelData = slice_data.tobytes()
        
        # 保存文件
        output_filename = os.path.join(output_dir, f"slice_{i:04d}.dcm")
        ds.save_as(output_filename, write_like_original=False)
        print(f"已保存切片 {i+1}/{num_slices}: {output_filename}")
    
    print(f"转换完成！共生成 {num_slices} 个DICOM文件")
    return num_slices


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="将NIfTI文件转换为DICOM格式")
    parser.add_argument("input", help="输入的NIfTI文件路径")
    parser.add_argument("-o", "--output", help="输出目录 (默认: input_filename_dicom)")
    parser.add_argument("-d", "--description", default="Converted from NIfTI", 
                       help="DICOM系列描述")
    parser.add_argument("--use-basic", action="store_true", 
                       help="使用基础转换方法（不使用SimpleITK）")
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.input):
        print(f"错误: 输入文件不存在: {args.input}")
        sys.exit(1)
    
    # 设置输出目录
    if args.output:
        output_dir = args.output
    else:
        base_name = os.path.splitext(os.path.splitext(os.path.basename(args.input))[0])[0]
        output_dir = f"{base_name}_dicom"
    
    print(f"输入文件: {args.input}")
    print(f"输出目录: {output_dir}")
    print(f"系列描述: {args.description}")
    
    try:
        if SITK_AVAILABLE and not args.use_basic:
            print("使用SimpleITK进行转换...")
            num_files = nifti_to_dicom_sitk(args.input, output_dir, args.description)
        else:
            print("使用基础方法进行转换...")
            num_files = nifti_to_dicom_basic(args.input, output_dir, args.description)
        
        print(f"\n✅ 转换成功完成！")
        print(f"生成了 {num_files} 个DICOM文件在目录: {output_dir}")
        
    except Exception as e:
        print(f"❌ 转换失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    # 如果直接运行脚本，可以在这里设置默认参数进行测试
    if len(sys.argv) == 1:
        # 默认转换AAL模板文件
        input_file = "output/my_result/AAL-Merged/AAL_Merged_Noncortical_False_withWM.nii.gz"
        if os.path.exists(input_file):
            print("检测到AAL模板文件，开始转换...")
            output_dir = "output/my_result/AAL-Merged/AAL_Merged_DICOM"
            series_description = "AAL Merged Noncortical False withWM"
            
            try:
                if SITK_AVAILABLE:
                    num_files = nifti_to_dicom_sitk(input_file, output_dir, series_description)
                else:
                    num_files = nifti_to_dicom_basic(input_file, output_dir, series_description)
                
                print(f"\n✅ 转换成功完成！")
                print(f"生成了 {num_files} 个DICOM文件在目录: {output_dir}")
            except Exception as e:
                print(f"❌ 转换失败: {str(e)}")
        else:
            print("未找到默认的AAL模板文件")
            print("用法: python ntod.py <nifti_file> [-o output_dir] [-d description] [--use-basic]")
    else:
        main()
