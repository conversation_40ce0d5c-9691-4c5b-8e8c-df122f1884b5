import SimpleITK as sitk
import numpy as np
import os
import csv

# 可选CuPy支持
try:
    import cupy as cp
    HAS_CUPY = True
except Exception:
    cp = None
    HAS_CUPY = False


def _get_xp(use_gpu: bool):
    return cp if (use_gpu and HAS_CUPY) else np


def _to_numpy_scalar(x, xp):
    # 将xp标量转为Python标量
    if xp is np:
        return float(x)
    else:
        return float(cp.asnumpy(x))


def _compute_hausdorff_gpu(gt_bool, seg_bool, spacing, xp):
    """
    使用GPU计算豪斯多夫距离的近似值
    基于距离变换的快速实现
    """
    try:
        if xp is np:
            # CPU fallback
            return _compute_hausdorff_cpu_fallback(gt_bool, seg_bool, spacing)

        # GPU实现 - 实际上由于scipy限制，我们使用混合方法
        from scipy.ndimage import distance_transform_edt, binary_erosion

        # 转换为CPU进行距离变换和边界检测
        gt_cpu = cp.asnumpy(gt_bool)
        seg_cpu = cp.asnumpy(seg_bool)

        # 检查是否有前景
        if np.sum(gt_cpu) == 0 or np.sum(seg_cpu) == 0:
            return 0.0

        # 计算距离变换
        gt_dt = distance_transform_edt(~gt_cpu, sampling=spacing)
        seg_dt = distance_transform_edt(~seg_cpu, sampling=spacing)

        # 计算边界（表面）
        gt_surface = gt_cpu & ~binary_erosion(gt_cpu)
        seg_surface = seg_cpu & ~binary_erosion(seg_cpu)

        # 计算有向豪斯多夫距离
        h1 = 0.0
        h2 = 0.0

        if np.sum(seg_surface) > 0:
            h1 = np.max(gt_dt[seg_surface])

        if np.sum(gt_surface) > 0:
            h2 = np.max(seg_dt[gt_surface])

        # 豪斯多夫距离是两个有向距离的最大值
        hausdorff = max(h1, h2)
        return float(hausdorff)

    except Exception as e:
        # GPU计算失败时回退到CPU
        print(f"GPU豪斯多夫距离计算失败，回退到CPU: {e}")
        return _compute_hausdorff_cpu_fallback(gt_bool, seg_bool, spacing)


def _compute_hausdorff_cpu_fallback(gt_bool, seg_bool, spacing):
    """CPU回退的豪斯多夫距离计算"""
    try:
        from scipy.ndimage import distance_transform_edt

        # 确保是numpy数组
        if hasattr(gt_bool, 'get'):  # CuPy数组
            gt_np = gt_bool.get()
            seg_np = seg_bool.get()
        else:
            gt_np = np.asarray(gt_bool)
            seg_np = np.asarray(seg_bool)

        # 计算距离变换
        gt_dt = distance_transform_edt(~gt_np, sampling=spacing)
        seg_dt = distance_transform_edt(~seg_np, sampling=spacing)

        # 计算边界点
        from scipy.ndimage import binary_erosion
        gt_surface = gt_np & ~binary_erosion(gt_np)
        seg_surface = seg_np & ~binary_erosion(seg_np)

        # 计算有向豪斯多夫距离
        if np.sum(seg_surface) > 0:
            h1 = np.max(gt_dt[seg_surface])
        else:
            h1 = 0.0

        if np.sum(gt_surface) > 0:
            h2 = np.max(seg_dt[gt_surface])
        else:
            h2 = 0.0

        return float(max(h1, h2))

    except Exception as e:
        print(f"CPU豪斯多夫距离计算也失败: {e}")
        return -1.0


def calculate_segmentation_metrics_manual(ground_truth_path, segmentation_path, label_range=(1, 130), use_gpu: bool = True):
    """
    计算并返回两个3D标签nii文件之间的分割评估指标。
    - 使用NumPy/CuPy手动计算指标，以兼容旧版SimpleITK并默认启用GPU加速。
    - 自动将多标签合并为二值化前景。
    - 自动处理物理空间不匹配的问题，将待评估图像重采样到金标准空间。
    - GPU加速主要用于Dice、Jaccard等指标计算，豪斯多夫距离使用SimpleITK。
    """
    
    try:
        gt_img_multi_label = sitk.ReadImage(ground_truth_path, sitk.sitkUInt8)
        seg_img_multi_label = sitk.ReadImage(segmentation_path, sitk.sitkUInt8)
    except Exception as e:
        print(f"错误：无法读取文件。请检查路径是否正确。 {e}")
        return None

    # 检查元数据并根据需要进行重采样
    metadata_mismatch = any([
        gt_img_multi_label.GetSize() != seg_img_multi_label.GetSize(),
        gt_img_multi_label.GetOrigin() != seg_img_multi_label.GetOrigin(),
        gt_img_multi_label.GetSpacing() != seg_img_multi_label.GetSpacing(),
        gt_img_multi_label.GetDirection() != seg_img_multi_label.GetDirection()
    ])

    seg_img_to_process = seg_img_multi_label
    if metadata_mismatch:
        print("警告：检测到元数据不匹配。正在重采样分割结果...")
        resampler = sitk.ResampleImageFilter()
        resampler.SetReferenceImage(gt_img_multi_label)
        resampler.SetInterpolator(sitk.sitkNearestNeighbor)
        resampler.SetDefaultPixelValue(0)
        seg_img_to_process = resampler.Execute(seg_img_multi_label)
        print("重采样完成。")

    # 将多标签图像二值化
    lower_bound, upper_bound = label_range
    inside_value = 1
    outside_value = 0
    gt_img_binary = sitk.BinaryThreshold(gt_img_multi_label, lowerThreshold=lower_bound, upperThreshold=upper_bound, insideValue=inside_value, outsideValue=outside_value)
    seg_img_binary = sitk.BinaryThreshold(seg_img_to_process, lowerThreshold=lower_bound, upperThreshold=upper_bound, insideValue=inside_value, outsideValue=outside_value)

    # --- 使用NumPy/CuPy手动计算指标 ---
    gt_np = sitk.GetArrayFromImage(gt_img_binary).astype(bool)
    seg_np = sitk.GetArrayFromImage(seg_img_binary).astype(bool)

    xp = _get_xp(use_gpu)
    gt_bool = xp.asarray(gt_np)
    seg_bool = xp.asarray(seg_np)

    tp = xp.sum(gt_bool & seg_bool)
    fp = xp.sum(~gt_bool & seg_bool)
    fn = xp.sum(gt_bool & ~seg_bool)

    denom_dice = (2 * tp + fp + fn)
    dice = (2 * tp) / denom_dice if _to_numpy_scalar(denom_dice > 0, xp) else 0.0
    denom_jac = (tp + fp + fn)
    jaccard = tp / denom_jac if _to_numpy_scalar(denom_jac > 0, xp) else 0.0
    denom_pre = (tp + fp)
    precision = tp / denom_pre if _to_numpy_scalar(denom_pre > 0, xp) else 0.0
    denom_sen = (tp + fn)
    sensitivity = tp / denom_sen if _to_numpy_scalar(denom_sen > 0, xp) else 0.0

    dice = _to_numpy_scalar(dice, xp)
    jaccard = _to_numpy_scalar(jaccard, xp)
    precision = _to_numpy_scalar(precision, xp)
    sensitivity = _to_numpy_scalar(sensitivity, xp)
    
    # Hausdorff距离计算 (优先使用SimpleITK，GPU主要用于其他指标)
    try:
        hausdorff_distance_filter = sitk.HausdorffDistanceImageFilter()
        hausdorff_distance_filter.Execute(gt_img_binary, seg_img_binary)
        hausdorff = hausdorff_distance_filter.GetHausdorffDistance()
    except Exception as e:
        print(f"计算Hausdorff距离时出错: {e}")
        hausdorff = -1.0

    metrics = {
        'Dice': dice,
        'Jaccard': jaccard,
        'Precision': precision,
        'Sensitivity': sensitivity,
        'Hausdorff': hausdorff
    }
    
    return metrics


# 我将新增：逐标签计算指标（支持GPU并可关闭逐标签Hausdorff）

def calculate_segmentation_metrics_per_label(ground_truth_path, segmentation_path, label_range=(1, 130), labels=None, use_gpu: bool = True, compute_hausdorff_per_label: bool = True):
    """
    对每个标签(ROI)分别计算指标，返回 {label: metrics_dict}。
    - 自动对齐到金标准的空间(必要时重采样)。
    - 若 labels 为空，则在金标准中自动检索给定范围内出现过的标签。
    - use_gpu=True 时，使用CuPy在GPU上进行TP/FP/FN等运算，豪斯多夫距离也支持GPU加速。
    - compute_hausdorff_per_label=True 默认启用逐标签豪斯多夫距离计算。
    """
    try:
        gt_img_multi_label = sitk.ReadImage(ground_truth_path, sitk.sitkUInt16)
        seg_img_multi_label = sitk.ReadImage(segmentation_path, sitk.sitkUInt16)
    except Exception as e:
        print(f"错误：无法读取文件。请检查路径是否正确。 {e}")
        return None

    metadata_mismatch = any([
        gt_img_multi_label.GetSize() != seg_img_multi_label.GetSize(),
        gt_img_multi_label.GetOrigin() != seg_img_multi_label.GetOrigin(),
        gt_img_multi_label.GetSpacing() != seg_img_multi_label.GetSpacing(),
        gt_img_multi_label.GetDirection() != seg_img_multi_label.GetDirection()
    ])

    seg_img_to_process = seg_img_multi_label
    if metadata_mismatch:
        print("警告：检测到元数据不匹配。正在重采样分割结果用于逐标签评估...")
        resampler = sitk.ResampleImageFilter()
        resampler.SetReferenceImage(gt_img_multi_label)
        resampler.SetInterpolator(sitk.sitkNearestNeighbor)
        resampler.SetDefaultPixelValue(0)
        seg_img_to_process = resampler.Execute(seg_img_multi_label)
        print("重采样完成。")

    # CPU端读取整图数组
    gt_all_np = sitk.GetArrayFromImage(gt_img_multi_label)
    seg_all_np = sitk.GetArrayFromImage(seg_img_to_process)

    lower_bound, upper_bound = label_range
    if labels is None:
        unique_labels = np.unique(gt_all_np)
        labels_to_eval = [int(l) for l in unique_labels if lower_bound <= l <= upper_bound]
        labels_to_eval = sorted([l for l in labels_to_eval if l != 0])
    else:
        labels_to_eval = [int(l) for l in labels if lower_bound <= int(l) <= upper_bound and int(l) != 0]
        labels_to_eval = sorted(set(labels_to_eval))

    xp = _get_xp(use_gpu)
    gt_all = xp.asarray(gt_all_np)
    seg_all = xp.asarray(seg_all_np)

    results_per_label = {}

    voxel_volume_mm3 = float(np.prod(list(gt_img_multi_label.GetSpacing()))) if gt_img_multi_label.GetSpacing() else 1.0

    for label in labels_to_eval:
        gt_np_bool = xp.equal(gt_all, label)
        seg_np_bool = xp.equal(seg_all, label)

        tp = xp.sum(gt_np_bool & seg_np_bool)
        fp = xp.sum(~gt_np_bool & seg_np_bool)
        fn = xp.sum(gt_np_bool & ~seg_np_bool)
        gt_count = xp.sum(gt_np_bool)
        seg_count = xp.sum(seg_np_bool)

        tp_py = _to_numpy_scalar(tp, xp)
        fp_py = _to_numpy_scalar(fp, xp)
        fn_py = _to_numpy_scalar(fn, xp)
        gt_count_py = int(_to_numpy_scalar(gt_count, xp))
        seg_count_py = int(_to_numpy_scalar(seg_count, xp))

        denom_dice = (2 * tp_py + fp_py + fn_py)
        dice = (2 * tp_py) / denom_dice if denom_dice > 0 else 0.0
        denom_jac = (tp_py + fp_py + fn_py)
        jaccard = tp_py / denom_jac if denom_jac > 0 else 0.0
        denom_pre = (tp_py + fp_py)
        precision = tp_py / denom_pre if denom_pre > 0 else 0.0
        denom_sen = (tp_py + fn_py)
        sensitivity = tp_py / denom_sen if denom_sen > 0 else 0.0

        # 逐标签Hausdorff计算（使用SimpleITK）
        hausdorff = -1.0
        if compute_hausdorff_per_label and (gt_count_py > 0 and seg_count_py > 0):
            try:
                gt_label_img = sitk.BinaryThreshold(gt_img_multi_label, lowerThreshold=float(label), upperThreshold=float(label), insideValue=1, outsideValue=0)
                seg_label_img = sitk.BinaryThreshold(seg_img_to_process, lowerThreshold=float(label), upperThreshold=float(label), insideValue=1, outsideValue=0)
                hausdorff_distance_filter = sitk.HausdorffDistanceImageFilter()
                hausdorff_distance_filter.Execute(gt_label_img, seg_label_img)
                hausdorff = float(hausdorff_distance_filter.GetHausdorffDistance())
            except Exception as e:
                print(f"计算标签 {label} 的Hausdorff距离时出错: {e}")
                hausdorff = -1.0

        results_per_label[label] = {
            'Dice': float(dice),
            'Jaccard': float(jaccard),
            'Precision': float(precision),
            'Sensitivity': float(sensitivity),
            'Hausdorff': float(hausdorff),
            'TP': int(tp_py),
            'FP': int(fp_py),
            'FN': int(fn_py),
            'GT_Voxels': int(gt_count_py),
            'Seg_Voxels': int(seg_count_py),
            'GT_Volume_mm3': float(gt_count_py * voxel_volume_mm3),
            'Seg_Volume_mm3': float(seg_count_py * voxel_volume_mm3)
        }

    return results_per_label


def export_metrics_per_label_to_csv(per_label_results, csv_path):
    """将逐标签结果导出为CSV。"""
    if not per_label_results:
        print("没有逐标签结果可导出。")
        return

    os.makedirs(os.path.dirname(csv_path), exist_ok=True)

    # 汇总所有键，确保列完整
    all_keys = set()
    for v in per_label_results.values():
        all_keys.update(v.keys())
    columns = ['Label'] + sorted(all_keys)

    with open(csv_path, 'w', newline='', encoding='utf-8-sig') as f:
        writer = csv.writer(f)
        writer.writerow(columns)
        for label in sorted(per_label_results.keys()):
            row = [label]
            metrics = per_label_results[label]
            for k in columns[1:]:
                row.append(metrics.get(k, ''))
            writer.writerow(row)

    print(f"逐标签结果已导出: {csv_path}")


def summarize_metrics(per_label_results):
    """计算逐标签指标的简单均值统计，返回字典。Hausdorff跳过-1。"""
    if not per_label_results:
        return {}
    keys_to_avg = ['Dice', 'Jaccard', 'Precision', 'Sensitivity', 'Hausdorff']
    agg = {}
    for k in keys_to_avg:
        vals = []
        for m in per_label_results.values():
            v = m.get(k, None)
            if v is None:
                continue
            if k == 'Hausdorff' and v < 0:
                continue
            vals.append(float(v))
        agg[f'Mean_{k}'] = float(np.mean(vals)) if len(vals) > 0 else float('nan')
    return agg


if __name__ == '__main__':
    file1_path = 'output/pmod_result/N30R83_1MM_2/parcel_s2_Brain_segments__Hammers_N30R83_1mm_atlas_region_labels____NORMALIZED_to_TOF_AC_SC_GF_MF_1_0____Atlas_in_Functional_space_Hammers_N30R83_1MM.nii'
    file2_path = 'output/my_result/Hammers-N30R83-1MM/Hammers-N30R83-1MM_withWM.nii.gz'
    label_range_to_evaluate = (1, 130)

    use_gpu = True if HAS_CUPY else False
    compute_hausdorff_per_label = True  # 启用逐标签豪斯多夫距离计算

    print(f"正在比较:\n  - 金标准 (GT): {file1_path}\n  - 分割结果 (Seg): {file2_path}")
    print(f"GPU加速: {'启用' if use_gpu else '未启用 (需要安装CuPy)'}，逐标签Hausdorff: {'开启' if compute_hausdorff_per_label else '关闭'}\n")
    
    try:
        # 整体前景合并指标（支持GPU）
        results = calculate_segmentation_metrics_manual(file1_path, file2_path, label_range=label_range_to_evaluate, use_gpu=use_gpu)

        if results:
            print("\n--- 计算结果 (手动兼容模式, 全前景合并) ---")
            print(f"评估的标签范围: {label_range_to_evaluate}")
            print(f"'Dice'      : {results['Dice']:.4f}")
            print(f"'Jaccard'   : {results['Jaccard']:.4f}")
            print(f"'Precision' : {results['Precision']:.4f}")
            print(f"'Sensitivity': {results['Sensitivity']:.4f}")
            print(f"'Hausdorff' : {results['Hausdorff']:.4f}")
            print("---------------------------------")

        # 逐标签（GPU路径用于TP/FP/FN等，Hausdorff默认关闭）
        per_label = calculate_segmentation_metrics_per_label(
            file1_path,
            file2_path,
            label_range=label_range_to_evaluate,
            labels=None,
            use_gpu=use_gpu,
            compute_hausdorff_per_label=compute_hausdorff_per_label
        )
        if per_label:
            print("\n--- 逐标签计算结果 ---")
            print(f"标签范围: {label_range_to_evaluate} (仅输出GT中出现的标签)")
            for label in sorted(per_label.keys()):
                m = per_label[label]
                print(f"Label {label:>3}: Dice={m['Dice']:.4f}, Jaccard={m['Jaccard']:.4f}, Precision={m['Precision']:.4f}, Sensitivity={m['Sensitivity']:.4f}, Hausdorff={m['Hausdorff']:.4f}, TP={m['TP']}, FP={m['FP']}, FN={m['FN']}, GT_Voxels={m['GT_Voxels']}, Seg_Voxels={m['Seg_Voxels']}")
            print("---------------------------------")

            # 导出CSV
            csv_out = os.path.join('output', 'metrics_per_label.csv')
            export_metrics_per_label_to_csv(per_label, csv_out)

            # 均值统计
            agg = summarize_metrics(per_label)
            if agg:
                print("\n--- 逐标签均值统计 ---")
                for k, v in agg.items():
                    print(f"{k}: {v:.4f}")
                print("---------------------------------")

    except Exception as e:
        print(f"\n在处理你的文件时发生严重错误: {e}")
        print("请检查文件路径和文件本身是否有效。")