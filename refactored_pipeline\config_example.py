# 示例配置文件 - 展示如何自定义参数
# 复制此文件为 config.py 并根据需要修改参数

# 概率阈值配置 - 可以根据需要调整这些值
PROBABILITY_THRESHOLDS = {
    'wm_threshold': 0.3,    # 白质概率阈值 - 降低到0.3以获得更多白质区域
    'gm_threshold': 0.7,    # 灰质概率阈值 - 提高到0.7以获得更严格的灰质区域
    'csf_threshold': 0.6,   # 脑脊液概率阈值 - 提高到0.6以更好地排除CSF
}

# 路径配置 - 根据实际数据位置修改
PATHS = {
    'moving_image_path': "centi/AD01_PiB_5070.nii",
    'fixed_image_path': "normalization/PET.nii.gz",
    'output_folder_path': "output/my_result/centi_test_aal",
    'template_path': "PMOD-VOI/AAL-MERGED-NEURO.nii.gz",
    'gm_template_path': "map/gm_prob_map.nii.gz",
    'csf_template_path': "map/csf_prob_map.nii.gz",
    'wm_template_path': "map/wm_prob_map.nii.gz",
    'aal_txt_path': "PMOD-VOI/AAL-MERGED-NEURO.txt",
}

# 处理参数配置
PROCESSING_PARAMS = {
    'gm_mask_noncortical': True,  # 是否对非皮层区域应用GM掩码
    'use_csf_prob': True,         # 是否使用CSF概率图谱
}

# 配准参数配置 - 高级用户可以根据需要调整
REGISTRATION_PARAMS = {
    'numberOfHistogramBins': 50,      # 直方图箱数
    'learningRate': 2.0,              # 学习率
    'minStep': 1e-6,                  # 最小步长
    'numberOfIterations': 300,        # 迭代次数
    'relaxationFactor': 0.8,          # 松弛因子
    'shrinkFactors': [8, 4, 2, 1],    # 多分辨率收缩因子
    'smoothingSigmas': [4, 2, 1, 0],  # 多分辨率平滑因子
}

# 使用说明：
# 1. 将此文件复制为 config.py
# 2. 根据需要修改 PROBABILITY_THRESHOLDS 中的值
# 3. 确保 PATHS 中的路径指向正确的文件位置
# 4. 运行 main.py 即可使用新的配置 