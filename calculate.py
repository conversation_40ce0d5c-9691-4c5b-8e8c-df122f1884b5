import os
import sys
import argparse
from typing import Optional, Dict

import pandas as pd


THRESHOLDS: Dict[str, float] = {
    "Dice": 0.7,           # 越大越好, 需 > 0.7
    "Jaccard": 0.6,       # 越大越好, 需 > 0.6
    "Precision": 0.7,     # 越大越好, 需 > 0.7
    "Sensitivity": 0.7,   # 越大越好, 需 > 0.7
    "Hausdorff": 6.0,     # 越小越好, 需 < 6.0 mm
}

WEIGHTS: Dict[str, float] = {
    "Dice": 0.40,
    "Hausdorff": 0.30,
    "Sensitivity": 0.125,
    "Precision": 0.125,
    "Jaccard": 0.05,
}


def compute_exceed_percent(value: float, metric: str) -> float:
    """计算相对阈值的超出百分比。

    - 对于 Dice/Jaccard/Precision/Sensitivity: (value - thr)/thr * 100
    - 对于 Hausdorff(越小越好): (thr - value)/thr * 100

    允许为负数，表示未达到阈值的欠缺百分比。
    """
    thr = THRESHOLDS[metric]
    if metric == "Hausdorff":
        return (thr - value) / thr * 100.0
    return (value - thr) / thr * 100.0


def parse_args(argv: Optional[list] = None) -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description=(
            "读取 Excel 结果，计算各指标相对阈值的超出百分比，并按权重汇总。"
        )
    )
    parser.add_argument(
        "--file",
        dest="file",
        type=str,
        default=os.path.join("output", "canshu.xlsx"),
        help="输入 Excel 文件路径 (默认: output/canshu.xlsx)",
    )
    parser.add_argument(
        "--sheet",
        dest="sheet",
        type=str,
        default=0,
        help="工作表名或索引 (默认: 0)",
    )
    parser.add_argument(
        "--outdir",
        dest="outdir",
        type=str,
        default="output",
        help="输出目录 (默认: output)",
    )
    return parser.parse_args(argv)


def normalize_columns(df: pd.DataFrame) -> pd.DataFrame:
    """统一列名，尽量兼容中英文与大小写。"""
    rename_map = {}
    for col in df.columns:
        key = str(col).strip()
        key_lower = key.lower()
        if key_lower in {"model", "模板", "name", "models", "模板名称"}:
            rename_map[col] = "Model"
        elif key_lower.startswith("dice"):
            rename_map[col] = "Dice"
        elif key_lower.startswith("jacc"):
            rename_map[col] = "Jaccard"
        elif key_lower.startswith("prec"):
            rename_map[col] = "Precision"
        elif key_lower.startswith("sens") or key_lower.startswith("recall"):
            rename_map[col] = "Sensitivity"
        elif key_lower.startswith("haus"):
            rename_map[col] = "Hausdorff"
    df = df.rename(columns=rename_map)
    return df


def main(argv: Optional[list] = None) -> None:
    args = parse_args(argv)

    if not os.path.exists(args.file):
        print(f"[ERROR] Excel 不存在: {args.file}")
        sys.exit(1)

    df = pd.read_excel(args.file, sheet_name=args.sheet)
    df = normalize_columns(df)

    required_cols = ["Model", "Dice", "Jaccard", "Precision", "Sensitivity", "Hausdorff"]
    for c in required_cols:
        if c not in df.columns:
            print(f"[ERROR] 缺少列: {c}")
            sys.exit(1)

    # 计算各项超出百分比
    for metric in ["Dice", "Jaccard", "Precision", "Sensitivity", "Hausdorff"]:
        df[f"{metric}_exceed_%"] = df[metric].apply(lambda v: compute_exceed_percent(float(v), metric))

    # 加权得分（单位: 百分比）
    df["Weighted_Exceed_%"] = (
        df["Dice_exceed_%"] * WEIGHTS["Dice"]
        + df["Hausdorff_exceed_%"] * WEIGHTS["Hausdorff"]
        + df["Sensitivity_exceed_%"] * WEIGHTS["Sensitivity"]
        + df["Precision_exceed_%"] * WEIGHTS["Precision"]
        + df["Jaccard_exceed_%"] * WEIGHTS["Jaccard"]
    )

    # 排序输出
    out_cols = [
        "Model",
        "Dice", "Dice_exceed_%",
        "Jaccard", "Jaccard_exceed_%",
        "Precision", "Precision_exceed_%",
        "Sensitivity", "Sensitivity_exceed_%",
        "Hausdorff", "Hausdorff_exceed_%",
        "Weighted_Exceed_%",
    ]
    result = df[out_cols].sort_values("Weighted_Exceed_%", ascending=False)

    os.makedirs(args.outdir, exist_ok=True)
    csv_path = os.path.join(args.outdir, "canshu_weighted.csv")
    xlsx_path = os.path.join(args.outdir, "canshu_weighted.xlsx")
    result.to_csv(csv_path, index=False, encoding="utf-8-sig")
    with pd.ExcelWriter(xlsx_path, engine="openpyxl") as writer:
        result.to_excel(writer, index=False)

    # 控制台友好打印
    print("按加权超出百分比(%)降序：")
    with pd.option_context('display.max_rows', None, 'display.max_columns', None):
        print(result)
    print(f"\n已保存: {csv_path}\n已保存: {xlsx_path}")


if __name__ == "__main__":
    main(sys.argv[1:])
