import SimpleITK as sitk
import os

# --- 1. 设置文件路径和参数 ---
# 输入文件：这四个文件都应该是已经对齐到PET空间的
aal_in_pet_path = "output/my_result/AAL-Merged/template_to_pet_inverse_affineonly.nii.gz"
gm_in_pet_path = "output/my_result/AAL-Merged/gm_template_to_pet_inverse_affineonly.nii.gz"
csf_in_pet_path = "output/my_result/AAL-Merged/csf_template_to_pet_inverse_affineonly.nii.gz"
# 新增：WM图谱的路径
wm_in_pet_path = "output/my_result/AAL-Merged/wm_template_to_pet_inverse_affineonly.nii.gz" 

# 输出文件：再次更新输出文件名，使其最准确地描述内容
output_intersected_path = "output/my_result/AAL-Merged/AAL_Merged_Noncortical_False.nii.gz" 

# 设定概率阈值 (对GM, CSF, 和 WM 都适用)
PROBABILITY_THRESHOLD = 0.5

# 新增：是否对非皮层区应用GM掩膜
GM_MASK_NONCORTICAL = False  # True=掩膜, False=不掩膜

# 非皮层区AAL标签名及其对应的标签值
NONCORTICAL_LABELS = {
    'Amygdala_l': 27,
    'Amygdala_r': 28,
    'CaudateNucl_l': 49,
    'CaudateNucl_r': 50,
    'Putamen_l': 51,
    'Putamen_r': 52,
    'Pallidum_l': 53,
    'Pallidum_r': 54,
    'Thalamus_l': 55,
    'Thalamus_r': 56,
    'Medulla': 68,
    'Midbrain': 69,
}
NONCORTICAL_LABEL_VALUES = set(NONCORTICAL_LABELS.values())

# 检查输出目录是否存在，如果不存在则创建
output_dir = os.path.dirname(output_intersected_path)
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# --- 2. 读取图像 ---
print("正在读取已对齐的AAL、GM、CSF和WM图谱...")
aal_img = sitk.ReadImage(aal_in_pet_path)
gm_prob_img = sitk.ReadImage(gm_in_pet_path, sitk.sitkFloat32)
csf_prob_img = sitk.ReadImage(csf_in_pet_path, sitk.sitkFloat32)
wm_prob_img = sitk.ReadImage(wm_in_pet_path, sitk.sitkFloat32) # <--- 新增

print(f"AAL模板图像大小: {aal_img.GetSize()}")
print(f"灰质图谱图像大小: {gm_prob_img.GetSize()}")
print(f"CSF图谱图像大小:  {csf_prob_img.GetSize()}")
print(f"白质图谱图像大小: {wm_prob_img.GetSize()}") # <--- 新增

# --- 3. 安全性检查：确保所有图像空间信息一致 ---
if not all(aal_img.GetSize() == img.GetSize() and \
           aal_img.GetSpacing() == img.GetSpacing() and \
           aal_img.GetOrigin() == img.GetOrigin() for img in [gm_prob_img, csf_prob_img, wm_prob_img]):
    print("错误：输入的AAL、GM、CSF和WM图谱的空间信息不匹配！")
    print("请确保它们都是从同一个参考图像（PET）逆变换得到的。")
    exit()

# --- 4. 核心操作：创建所有蒙版并组合 ---

import numpy as np

# 步骤 A: 创建"高灰质"蒙版 (GM概率 >= 0.5)
print(f"正在创建高灰质(GM)蒙版 (概率 >= {PROBABILITY_THRESHOLD})...")
gm_mask = sitk.BinaryThreshold(gm_prob_img, lowerThreshold=PROBABILITY_THRESHOLD, upperThreshold=1e9, insideValue=1, outsideValue=0)

# 步骤 B: 创建"非CSF"蒙版 (CSF概率 < 0.5)
print(f"正在创建非脑脊液(CSF)蒙版 (概率 < {PROBABILITY_THRESHOLD})...")
high_csf_mask = sitk.BinaryThreshold(csf_prob_img, lowerThreshold=PROBABILITY_THRESHOLD, upperThreshold=1e9, insideValue=1, outsideValue=0)
not_csf_mask = sitk.Not(high_csf_mask)

# 步骤 C: 组合GM和非CSF蒙版，得到最终的"纯灰质"蒙版
print("正在组合GM和非CSF蒙版以生成最终的'纯灰质'蒙版...")
final_mask = sitk.Multiply(gm_mask, not_csf_mask)

# 步骤 D: 将最终蒙版的数据类型转换为与AAL模板一致
final_mask = sitk.Cast(final_mask, aal_img.GetPixelID())

# 步骤 E: 将AAL模板与最终的"纯灰质"蒙版相交（根据GM_MASK_NONCORTICAL选项调整）
print("正在将AAL模板与最终的组合蒙版相交...")

# 新增：根据GM_MASK_NONCORTICAL控制非皮层区掩膜
if GM_MASK_NONCORTICAL:
    # 原逻辑：所有区域都掩膜
    intersected_aal = sitk.Multiply(aal_img, final_mask)
else:
    # 非皮层区不掩膜，皮层区掩膜
    aal_np = sitk.GetArrayFromImage(aal_img)
    mask_np = sitk.GetArrayFromImage(final_mask)
    out_np = np.zeros_like(aal_np)
    # 皮层区（不在非皮层区标签值里）
    cortex_mask = ~np.isin(aal_np, list(NONCORTICAL_LABEL_VALUES))
    out_np[cortex_mask] = aal_np[cortex_mask] * mask_np[cortex_mask]
    # 非皮层区，直接保留AAL值
    noncortex_mask = np.isin(aal_np, list(NONCORTICAL_LABEL_VALUES))
    out_np[noncortex_mask] = aal_np[noncortex_mask]
    # 转回SimpleITK
    intersected_aal = sitk.GetImageFromArray(out_np)
    intersected_aal.CopyInformation(aal_img)

# 新增：保存没有白质补全的图像
sitk.WriteImage(intersected_aal, output_intersected_path)
print(f"已保存无白质补全的AAL+纯灰质模板: {output_intersected_path}")

# --- 6. 补全白质区域到AAL未覆盖区域 ---
print("正在补全AAL模板未覆盖的白质区域...")
# 生成白质掩膜（概率>=0.5）
wm_mask = sitk.BinaryThreshold(wm_prob_img, lowerThreshold=PROBABILITY_THRESHOLD, upperThreshold=1e9, insideValue=1, outsideValue=0)
# 找到“纯灰质AAL”未覆盖区域（即相交后为0的地方）
intersected_zero_mask = sitk.BinaryThreshold(intersected_aal, lowerThreshold=0, upperThreshold=0, insideValue=1, outsideValue=0)
# 只在“纯灰质AAL”==0且白质==1的地方填充
wm_fill_mask = sitk.And(wm_mask, intersected_zero_mask)
# 创建一个全零图像，填充白质标签80
wm_fill_img = sitk.Cast(wm_fill_mask, sitk.sitkUInt8) * 80
# 转换为与intersected_aal一致的数据类型
wm_fill_img = sitk.Cast(wm_fill_img, intersected_aal.GetPixelID())
# 合成最终模板（AAL与纯灰质相交结果+白质填充）
final_with_wm = sitk.Add(intersected_aal, wm_fill_img)

# --- 7. 保存最终结果 ---
final_output_path = output_intersected_path.replace('.nii', '_withWM.nii')
sitk.WriteImage(final_with_wm, final_output_path)
print("-" * 60)
print(f"成功！最终AAL+灰质+白质模板已保存至:\n{final_output_path}")
print(f"白质区域用标签80，仅补全AAL模板为0且白质概率>={PROBABILITY_THRESHOLD}的区域。")
print("-" * 60)